// Multi-Segment Light Guide Design
// Combines multiple segment types for comprehensive geometry

Application:General
{
 ExportType="IGS";

 Source:Point
 {
  Position=0 0 0;
  Direction=1 0 0;
  RayCount=15000;
 }

 Detector:Plane
 {
  ReferencePoint=120 0 0;
 }

 // Input coupling surface - Plain segment
 Segment:Plain
 {
  StartPoint=5 0 0;
  HorizontalSize=8;
  VerticalSize=8;
  Spread=0 0;

  Source:Point
  {
   Position=0 0 0;
   Direction=1 0 0;
  }

  Detector:Plane
  {
   ReferencePoint=120 0 0;
  }
 }

 // Main reflector with elements
 Segment:Elements
 {
  PillowType="Pillow";

  Source:Point
  {
   Position=0 0 0;
   Direction=1 0 0;
  }

  Detector:Plane
  {
   ReferencePoint=120 0 0;
  }

  BaseSurface:Ellipsoid
  {
   CenterPoint=60 0 0;
   SemiaxisA=30;
   SemiaxisB=12;
   SemiaxisC=12;
  }

  Grid:Rectangular
  {
   StartPoint=60 0 0;
   Uextent=-25 25;
   UCellCount=20;
   Vextent=-10 10;
   VCellCount=8;
   Distance=Projected;
  }

  Element:Pillow
  {
   FocalDistance=30;
   FocalPoint=0 0 0;
   upos=0 1;
   vpos=0 1;
   usize=2.5;
   vsize=2.5;
   udepth=0.3;
   vdepth=0.3;
  }
 }

 // Exit surface - Another Plain segment
 Segment:Plain
 {
  StartPoint=115 0 0;
  HorizontalSize=20;
  VerticalSize=20;
  Spread=0 0;

  Source:Point
  {
   Position=0 0 0;
   Direction=1 0 0;
  }

  Detector:Plane
  {
   ReferencePoint=120 0 0;
  }
 }
}
