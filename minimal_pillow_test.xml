<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
  <Application type="General">
    <ExportType>igs</ExportType>
    
    <!-- Main Source -->
    <Source type="Point">
      <Position x="0" y="0" z="-10"/>
    </Source>
    
    <!-- Main Detector -->
    <Detector type="Plane">
      <ReferencePoint x="0" y="0" z="20"/>
    </Detector>
    
    <!-- Minimal Segment with only required parameters -->
    <Segment name="MinimalPillowTest" type="Elements">
      
      <!-- Segment Source -->
      <Source type="Point">
        <Position x="0" y="0" z="-5"/>
      </Source>
      
      <!-- Segment Detector -->
      <Detector type="Plane">
        <ReferencePoint x="0" y="0" z="15"/>
      </Detector>
      
      <!-- Plane BaseSurface -->
      <BaseSurface type="Plane" name="TestPlane">
        <ReferencePoint x="0" y="0" z="0"/>
        <Normal x="0" y="0" z="1"/>
        <usize>20.0</usize>
        <vsize>20.0</vsize>
      </BaseSurface>
      
      <!-- Grid -->
      <Grid type="Rectangular">
        <StartPoint x="-5" y="-5" z="0"/>
        <Uextent>-5 5</Uextent>
        <Vextent>-5 5</Vextent>
        <UCellCount>5</UCellCount>
        <VCellCount>5</VCellCount>
      </Grid>
      
      <!-- Required parameters for ElementsSegment -->
      <PillowType>Pillow</PillowType>
      <HorizontalSpread>20.0</HorizontalSpread>
      <VerticalSpread>10.0</VerticalSpread>
      <LightDistribution>Lambertian</LightDistribution>
      <Correction>0.25</Correction>
      <Convexity>1</Convexity>
      
    </Segment>
    
  </Application>
</Configuration>
