Application:General
{
  Source:Directional{Direction=1 0 0;}
  Detector:Angular{Direction=1 0 0; Up=0 0 1;}
  
  Lens:Elements(LensPillow)
  {
    Color=red;
    
    InnerPlanePoint=0 0 0;
    OuterPlanePoint=2 0 0;
    HSpreadTable=20;
    VSpreadTable=10;
    LightDistribution=-0.15;
    IndexOfRefraction=1.586;
    Relatively=On;
    OnOuterSurface=On;
    ConnectingSurfaces=Off;
    ConnectingRatio=0.2;

    Benevolent=On;
    Stepped=Off;

    Grid:UVCurves
    {
      IGSFile=..\GRID1.igs;
      Trimming=Off;
      Distance=OnSurface;

      UCellCount=53;
      VCellCount=19;
    }
  }
}