<?xml version="1.0" encoding="UTF-8"?>
<Configuration version="1.0">
  <!-- Working LightGuide Configuration -->
  
  <Application name="LightGuideTest" type="General">
    <ExportType>IGS</ExportType>
    
    <!-- Main light source -->
    <Source type="Rectangle">
      <Position x="0" y="0" z="0"/>
      <Direction x="1" y="0" z="0"/>
      <Axis1 x="0" y="1" z="0"/>
      <Axis2 x="0" y="0" z="1"/>
      <Size1>10</Size1>
      <Size2>8</Size2>
    </Source>
    
    <!-- Output detector -->
    <Detector type="Plane">
      <ReferencePoint x="50" y="0" z="0"/>
      <Normal x="-1" y="0" z="0"/>
    </Detector>
    
    <!-- LightGuide Optical Engine -->
    <Optical type="LightGuide">
      
      <!-- MANDATORY PARAMETERS (from GetInputParameters) -->
      <TeethType>OnSurface</TeethType>
      <TAngle1>45 45</TAngle1>
      <TAngle2>45 45</TAngle2>
      <TDistance>1 1</TDistance>
      <TSize>50 50</TSize>
      <ReferencePoint x="25" y="0" z="0"/>
      
      <!-- MANDATORY BaseSurface section -->
      <BaseSurface type="Plane">
        <ReferencePoint x="25" y="0" z="0"/>
        <Normal x="0" y="0" z="1"/>
      </BaseSurface>
      
      <!-- OPTIONAL PARAMETERS -->
      <export_solid>on</export_solid>
      <export_steps>off</export_steps>
      <TOffset1>1</TOffset1>
      <OpticalDir x="1" y="0" z="0"/>
      <Thickness>2.0</Thickness>
      
    </Optical>
    
  </Application>
</Configuration>
