<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://optonit.com/config"
           xmlns:tns="http://optonit.com/config"
           elementFormDefault="qualified">

  <!-- Root Configuration Element -->
  <xs:element name="Configuration">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="Application" type="tns:ApplicationType"/>
      </xs:sequence>
      <xs:attribute name="version" type="xs:string" default="1.0"/>
    </xs:complexType>
  </xs:element>

  <!-- Application Type -->
  <xs:complexType name="ApplicationType">
    <xs:sequence>
      <xs:element name="ExportPath" type="xs:string" minOccurs="0"/>
      <!-- Path where exported files will be saved -->
      <xs:element name="ExportType" type="xs:string" minOccurs="0" default="IGS"/>
      <!-- Valid values: IGS, BGF, STP, or combinations like "IGS BGF STP" -->
      <xs:element name="Info" type="xs:string" minOccurs="0"/>
      <!-- Application information -->
      <xs:element name="Source" type="tns:SourceType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Detector" type="tns:DetectorType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Transformation" type="tns:TransformationType" minOccurs="0"/>
      <xs:element name="Segment" type="tns:SegmentType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Lens" type="tns:LensType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Optical" type="tns:OpticalType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Pipe" type="tns:PipeType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="LineEntity" type="tns:LineEntityType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="PathEntity" type="tns:PathEntityType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Cutter" type="tns:CutterType" minOccurs="0"/>
      <xs:element name="Raytrace" type="tns:RaytraceType" minOccurs="0"/>
      <xs:element name="PostProcessing" type="tns:PostProcessingType" minOccurs="0"/>
      <xs:element name="Materials" type="tns:MaterialsType" minOccurs="0"/>
      <xs:element name="Utility" type="tns:UtilityType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
    <xs:attribute name="name" type="xs:string"/>
    <xs:attribute name="type" type="xs:string" use="required"/>
  </xs:complexType>

  <!-- Source Types -->
  <xs:complexType name="SourceType">
    <xs:sequence>
      <xs:element name="Position" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="Direction" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="Up" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="RayCount" type="xs:int" minOccurs="0"/>
      <xs:element name="Axis" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="Axis1" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="Axis2" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="Length" type="xs:double" minOccurs="0"/>
      <xs:element name="Diameter" type="xs:double" minOccurs="0"/>
      <xs:element name="Width" type="xs:double" minOccurs="0"/>
      <xs:element name="Height" type="xs:double" minOccurs="0"/>
      <xs:element name="Size1" type="xs:double" minOccurs="0"/>
      <xs:element name="Size2" type="xs:double" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="type" type="xs:string" use="required"/>
  </xs:complexType>

  <!-- Detector Types -->
  <xs:complexType name="DetectorType">
    <xs:sequence>
      <xs:element name="Position" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="Direction" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="Up" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="ReferencePoint" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="Width" type="xs:double" minOccurs="0"/>
      <xs:element name="Height" type="xs:double" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="type" type="xs:string" use="required"/>
  </xs:complexType>

  <!-- Transformation Type -->
  <xs:complexType name="TransformationType">
    <xs:sequence>
      <xs:element name="Translation" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="Rotation" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="Scale" type="xs:double" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Segment Type -->
  <xs:complexType name="SegmentType">
    <xs:sequence>
      <xs:element name="Size" type="xs:double" minOccurs="0"/>
      <xs:element name="StartPoint" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="StartPointPar" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="HorizontalSize" type="xs:double" minOccurs="0"/>
      <xs:element name="VerticalSize" type="xs:double" minOccurs="0"/>
      <xs:element name="HorizontalSpread" type="xs:double" minOccurs="0"/>
      <xs:element name="VerticalSpread" type="xs:double" minOccurs="0"/>
      <xs:element name="LightDistribution" type="xs:string" minOccurs="0"/>
      <xs:element name="Correction" type="xs:double" minOccurs="0"/>
      <xs:element name="Convexity" type="xs:double" minOccurs="0"/>
      <xs:element name="PillowType" type="xs:string" minOccurs="0"/>
      <xs:element name="Spread" type="tns:Vector2Type" minOccurs="0"/>
      <xs:element name="Uextent" type="tns:Vector2Type" minOccurs="0"/>
      <xs:element name="Vextent" type="tns:Vector2Type" minOccurs="0"/>
      <xs:element name="UCellCount" type="xs:int" minOccurs="0"/>
      <xs:element name="VCellCount" type="xs:int" minOccurs="0"/>
      <xs:element name="Radius" type="xs:double" minOccurs="0"/>
      <xs:element name="FixedTangent" type="xs:boolean" minOccurs="0"/>
      
      <xs:element name="Source" type="tns:SourceType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Detector" type="tns:DetectorType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="BaseSurface" type="tns:BaseSurfaceType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Grid" type="tns:GridType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="SpaceGrid" type="tns:GridType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Element" type="tns:ElementType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Distribution" type="tns:DistributionType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="LineEntity" type="tns:LineEntityType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="PathEntity" type="tns:PathEntityType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Optical" type="tns:OpticalType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="PostProcessing" type="tns:PostProcessingType" minOccurs="0"/>
      <xs:element name="Transformation" type="tns:TransformationType" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="name" type="xs:string" use="required"/>
    <xs:attribute name="type" type="xs:string"/>
  </xs:complexType>

  <!-- BaseSurface Type -->
  <xs:complexType name="BaseSurfaceType">
    <xs:sequence>
      <!-- Common surface parameters -->
      <xs:element name="CenterPoint" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="ReferencePoint" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="Normal" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="Position" type="tns:Vector3Type" minOccurs="0"/>

      <!-- Ellipsoid, Hyperboloid parameters -->
      <xs:element name="SemiaxisA" type="xs:double" minOccurs="0"/>
      <xs:element name="SemiaxisB" type="xs:double" minOccurs="0"/>
      <xs:element name="SemiaxisC" type="xs:double" minOccurs="0"/>

      <!-- Paraboloid, FFParaboloid parameters -->
      <xs:element name="FocalPoint" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="FocalDistance" type="xs:double" minOccurs="0"/>
      <xs:element name="focal_point" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="focal_distance" type="xs:double" minOccurs="0"/>
      <xs:element name="axis" type="xs:string" minOccurs="0"/>

      <!-- Cylinder, general parameters -->
      <xs:element name="Axis" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="Diameter" type="xs:double" minOccurs="0"/>
      <xs:element name="Radius" type="xs:double" minOccurs="0"/>
      <xs:element name="radius" type="xs:double" minOccurs="0"/>

      <!-- Saddle parameters -->
      <xs:element name="Vertex" type="tns:Vector3Type" minOccurs="0"/>

      <!-- Common BaseSurface parameters -->
      <xs:element name="usize" type="xs:double" minOccurs="0"/>
      <xs:element name="vsize" type="xs:double" minOccurs="0"/>

      <!-- CADImport parameters -->
      <xs:element name="IGSFile" type="xs:string" minOccurs="0"/>
      <xs:element name="BGFFile" type="xs:string" minOccurs="0"/>
      <xs:element name="STPFile" type="xs:string" minOccurs="0"/>
      <xs:element name="Trimming" type="xs:boolean" minOccurs="0"/>

      <!-- Nested elements -->
      <xs:element name="SpaceGrid" type="tns:GridType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Grid" type="tns:GridType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="SurfaceTransformation" type="tns:TransformationType" minOccurs="0"/>
      <xs:element name="Transformation" type="tns:TransformationType" minOccurs="0"/>
      <xs:element name="Segment" type="tns:SegmentType" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="type" type="xs:string" use="required"/>
    <xs:attribute name="name" type="xs:string"/>
  </xs:complexType>

  <!-- Grid Type (includes SpaceGrid) -->
  <xs:complexType name="GridType">
    <xs:sequence>
      <xs:element name="StartPoint" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="LineSegmentDirection" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="Width" type="xs:double" minOccurs="0"/>
      <xs:element name="Height" type="xs:double" minOccurs="0"/>
      <xs:element name="Size" type="tns:Vector2Type" minOccurs="0"/>
      <xs:element name="Extent" type="tns:Vector2Type" minOccurs="0"/>
      <xs:element name="Count" type="tns:Vector2Type" minOccurs="0"/>
      <xs:element name="Resolution" type="tns:Vector2Type" minOccurs="0"/>
      <xs:element name="UCellCount" type="xs:string" minOccurs="0"/>
      <xs:element name="VCellCount" type="xs:string" minOccurs="0"/>
      <xs:element name="Ucount" type="xs:int" minOccurs="0"/>
      <xs:element name="Vcount" type="xs:int" minOccurs="0"/>
      <xs:element name="Uextent" type="tns:Vector2Type" minOccurs="0"/>
      <xs:element name="Vextent" type="tns:Vector2Type" minOccurs="0"/>
      <xs:element name="Distance" type="xs:string" minOccurs="0"/>
      <xs:element name="Scale" type="xs:double" minOccurs="0"/>
      <xs:element name="XCell" type="xs:boolean" minOccurs="0"/>
      <xs:element name="IGSFile" type="xs:string" minOccurs="0"/>
      <xs:element name="BGFFile" type="xs:string" minOccurs="0"/>
      <xs:element name="STPFile" type="xs:string" minOccurs="0"/>
      <xs:element name="Trimming" type="xs:boolean" minOccurs="0"/>
      <!-- Curves grid parameters -->
      <xs:element name="GridCurveFamily1" type="xs:string" minOccurs="0"/>
      <xs:element name="PillowsSize" type="xs:double" minOccurs="0"/>
      <xs:element name="AlignPillows" type="xs:boolean" minOccurs="0"/>
      <xs:element name="NumberOfPillows" type="xs:int" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="type" type="xs:string" use="required"/>
    <xs:attribute name="name" type="xs:string"/>
  </xs:complexType>

  <!-- Element Type -->
  <xs:complexType name="ElementType">
    <xs:sequence>
      <xs:element name="FocalDistance" type="xs:double" minOccurs="0"/>
      <xs:element name="FocalPoint" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="upos" type="tns:Vector2Type" minOccurs="0"/>
      <xs:element name="vpos" type="tns:Vector2Type" minOccurs="0"/>
      <xs:element name="usize" type="xs:double" minOccurs="0"/>
      <xs:element name="vsize" type="xs:double" minOccurs="0"/>
      <xs:element name="udepth" type="xs:double" minOccurs="0"/>
      <xs:element name="vdepth" type="xs:double" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="type" type="xs:string" use="required"/>
  </xs:complexType>

  <!-- Distribution Type -->
  <xs:complexType name="DistributionType">
    <xs:sequence>
      <xs:element name="h" type="xs:double" minOccurs="0"/>
      <xs:element name="v" type="xs:double" minOccurs="0"/>
      <xs:element name="w" type="xs:double" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="type" type="xs:string" use="required"/>
  </xs:complexType>

  <!-- Lens Type -->
  <xs:complexType name="LensType">
    <xs:sequence>
      <xs:element name="Color" type="xs:string" minOccurs="0"/>
      <xs:element name="HorizontalSpread" type="xs:double" minOccurs="0"/>
      <xs:element name="VerticalSpread" type="xs:double" minOccurs="0"/>
      <xs:element name="InnerPlanePoint" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="OuterPlanePoint" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="HSpreadTable" type="xs:string" minOccurs="0"/>
      <xs:element name="VSpreadTable" type="xs:string" minOccurs="0"/>
      <xs:element name="HRotationTable" type="xs:string" minOccurs="0"/>
      <xs:element name="VRotationTable" type="xs:string" minOccurs="0"/>
      <xs:element name="LightDistribution" type="xs:double" minOccurs="0"/>
      <xs:element name="IndexOfRefraction" type="xs:double" minOccurs="0"/>
      <xs:element name="Relatively" type="xs:boolean" minOccurs="0"/>
      <xs:element name="OnOuterSurface" type="xs:boolean" minOccurs="0"/>
      <xs:element name="OnSurface" type="xs:string" minOccurs="0"/>
      <xs:element name="OuterIGSFile" type="xs:string" minOccurs="0"/>
      <xs:element name="ConnectingSurfaces" type="xs:boolean" minOccurs="0"/>
      <xs:element name="ConnectingRatio" type="xs:double" minOccurs="0"/>
      <xs:element name="Benevolent" type="xs:boolean" minOccurs="0"/>
      <xs:element name="Stepped" type="xs:boolean" minOccurs="0"/>
      <xs:element name="Staple" type="xs:boolean" minOccurs="0"/>
      <xs:element name="StepsInMaterial" type="xs:boolean" minOccurs="0"/>
      <xs:element name="PillowType" type="xs:string" minOccurs="0"/>
      <xs:element name="Grid" type="tns:GridType" minOccurs="0"/>
      <xs:element name="PostProcessing" type="tns:PostProcessingType" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="name" type="xs:string" use="required"/>
    <xs:attribute name="type" type="xs:string"/>
  </xs:complexType>

  <!-- Optical Type -->
  <xs:complexType name="OpticalType">
    <xs:sequence>
      <!-- Material properties -->
      <xs:element name="ior" type="xs:double" minOccurs="0"/>
      <xs:element name="IndexOfRefraction" type="xs:double" minOccurs="0"/>

      <!-- Geometric parameters for AdvCollimator -->
      <xs:element name="CentralDistance" type="xs:double" minOccurs="0"/>
      <xs:element name="CentralDiameter" type="xs:double" minOccurs="0"/>
      <xs:element name="CentralSpread" type="xs:double" minOccurs="0"/>
      <xs:element name="EdgeDistance" type="xs:double" minOccurs="0"/>
      <xs:element name="FrontDistance" type="xs:double" minOccurs="0"/>
      <xs:element name="ReflectorDiameter" type="xs:double" minOccurs="0"/>
      <xs:element name="ReflectorSpread" type="xs:double" minOccurs="0"/>
      <xs:element name="LensExitSpread" type="xs:double" minOccurs="0"/>
      <xs:element name="Draft" type="xs:double" minOccurs="0"/>

      <!-- Lens configuration -->
      <xs:element name="LensEntryType" minOccurs="0">
        <xs:complexType mixed="true">
          <xs:sequence>
            <xs:element name="LensEntryFocalPointLateralShift" type="xs:double" minOccurs="0"/>
            <xs:element name="LensEntryFocalPointLongitudinalShift" type="xs:double" minOccurs="0"/>
          </xs:sequence>
          <xs:attribute name="type" type="xs:string"/>
        </xs:complexType>
      </xs:element>
      <xs:element name="LensExitType" minOccurs="0">
        <xs:complexType mixed="true">
          <xs:sequence>
            <xs:element name="LensExitLength" type="xs:double" minOccurs="0"/>
          </xs:sequence>
          <xs:attribute name="type" type="xs:string"/>
        </xs:complexType>
      </xs:element>
      <xs:element name="SideEntryType" type="xs:string" minOccurs="0"/>
      <xs:element name="TIRExitType" type="xs:string" minOccurs="0"/>
      <xs:element name="LensEntryFocalPointLateralShift" type="xs:double" minOccurs="0"/>
      <xs:element name="LensEntryFocalPointLongitudinalShift" type="xs:double" minOccurs="0"/>
      <xs:element name="LensExitLength" type="xs:double" minOccurs="0"/>

      <!-- Nested elements -->
      <xs:element name="Source" type="tns:SourceType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Detector" type="tns:DetectorType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Distribution" type="tns:DistributionType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Transformation" type="tns:TransformationType" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="type" type="xs:string" use="required"/>
    <xs:attribute name="name" type="xs:string"/>
  </xs:complexType>

  <!-- Pipe Type -->
  <xs:complexType name="PipeType">
    <xs:sequence>
      <xs:element name="Source" type="tns:SourceType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Detector" type="tns:DetectorType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="BaseSurface" type="tns:BaseSurfaceType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Grid" type="tns:GridType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Distribution" type="tns:DistributionType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Transformation" type="tns:TransformationType" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="type" type="xs:string" use="required"/>
    <xs:attribute name="name" type="xs:string"/>
  </xs:complexType>

  <!-- Cutter Type -->
  <xs:complexType name="CutterType">
    <xs:sequence>
      <xs:element name="CutterType" type="xs:string" minOccurs="0"/>
      <xs:element name="CutterParameters" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Raytrace Type -->
  <xs:complexType name="RaytraceType">
    <xs:sequence>
      <xs:element name="RaytraceParameters" type="xs:string" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Materials Type -->
  <xs:complexType name="MaterialsType">
    <xs:sequence>
      <xs:element name="Material" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <!-- LineEntity Type -->
  <xs:complexType name="LineEntityType">
    <xs:sequence>
      <xs:element name="StartPoint" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="EndPoint" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="Direction" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="Length" type="xs:double" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="type" type="xs:string" use="required"/>
    <xs:attribute name="name" type="xs:string"/>
  </xs:complexType>

  <!-- PathEntity Type -->
  <xs:complexType name="PathEntityType">
    <xs:sequence>
      <xs:element name="Points" type="xs:string" minOccurs="0"/>
      <xs:element name="PathType" type="xs:string" minOccurs="0"/>
      <xs:element name="Closed" type="xs:boolean" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="type" type="xs:string" use="required"/>
    <xs:attribute name="name" type="xs:string"/>
  </xs:complexType>

  <!-- Utility Type -->
  <xs:complexType name="UtilityType">
    <xs:sequence>
      <xs:element name="Source" type="tns:SourceType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Detector" type="tns:DetectorType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Distribution" type="tns:DistributionType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Transformation" type="tns:TransformationType" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="type" type="xs:string" use="required"/>
    <xs:attribute name="name" type="xs:string"/>
  </xs:complexType>

  <!-- PostProcessing Type -->
  <xs:complexType name="PostProcessingType">
    <xs:sequence>
      <xs:element name="StapleTolerance" type="xs:double" minOccurs="0"/>
      <xs:element name="ExtractingAngle" type="xs:double" minOccurs="0"/>
      <xs:element name="ExtractingDirection" type="tns:Vector3Type" minOccurs="0"/>
      <xs:element name="ConnectingSurfaces" type="xs:boolean" minOccurs="0"/>
      <xs:element name="ConnectingRatio" type="xs:double" minOccurs="0"/>
      <xs:element name="ConnectingDistance" type="xs:double" minOccurs="0"/>
      <xs:element name="ConnectingAngle" type="xs:double" minOccurs="0"/>
      <xs:element name="DraftAngle" type="xs:double" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Basic Types -->
  <xs:complexType name="Vector3Type">
    <xs:attribute name="x" type="xs:double" use="required"/>
    <xs:attribute name="y" type="xs:double" use="required"/>
    <xs:attribute name="z" type="xs:double" use="required"/>
  </xs:complexType>

  <xs:complexType name="Vector2Type">
    <xs:attribute name="u" type="xs:double" use="required"/>
    <xs:attribute name="v" type="xs:double" use="required"/>
  </xs:complexType>

</xs:schema>
