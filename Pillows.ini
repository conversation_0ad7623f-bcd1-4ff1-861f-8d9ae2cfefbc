[Application]
name=General
type=XPillows

[Source]
type=Directional
Direction=1 0 0

[Detector]
type=Angular
Direction=1 0 0
Up=0 0 1

[Lens]
name=Pillows_J
type=Elements
HorizontalSpread=-35
VerticalSpread=-35
InnerPlanePoint=-1400 0 0
OuterIGSFile=../Support.igs
OnOuterSurface=On
IndexOfRefraction=1.586
LightDistribution=-0.05
Staple=Off
Stepped=On
Benevolent=Off
PillowType=LensPillow
OnSurface=Outer

# Tables converted to INI format:
HSpreadTable_Header=0 3
HSpreadTable_Row1=0 -35 -35
HSpreadTable_Row2=10 -35 -35
HSpreadTable_Row3=14 -35 -35
HSpreadTable_Row4=20 -28 -28
HSpreadTable_Row5=56 -28 -28

VSpreadTable_Header=0 3
VSpreadTable_Row1=0 -27 -27
VSpreadTable_Row2=14 -30 -30
VSpreadTable_Row3=20 -37 -37
VSpreadTable_Row4=56 -37 -37

HRotationTable_Header=0 3
HRotationTable_Row1=0 -5 -5
HRotationTable_Row2=7 4.5 4.5
HRotationTable_Row3=8 -4.5 -4.5
HRotationTable_Row4=17 4.5 4.5

VRotationTable_Header=0 6
VRotationTable_Row1=0 0 0
VRotationTable_Row2=7 0 0
VRotationTable_Row3=8 0 0

[LensSource]
type=Directional
Direction=1 0 0

[LensGrid]
type=Rectangular
StartPoint=-484.99 -702.73 519.70
Uextent=-30 30
Vextent=-30 30
UCellCount=40
VCellCount=40

[PostProcessing]
StapleTolerance=0.05
ConnectingSurfaces=On
ConnectingRatio=1
ConnectingDistance=1
ConnectingAngle=60
DraftAngle=1

# Commented out parameters from original:
# ExtractingAngle=2
# ExtractingDirection=1 0 0
# StepsInMaterial=On
