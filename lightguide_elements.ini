# Comprehensive Light Guide with Optical Elements
# Uses Segment:Elements to create a reflector with optical elements

[Application]
name=General
ExportType=IGS

[Source]
type=Rectangle
Position=0 0 0
Direction=1 0 0
Up=0 0 1
Width=5
Height=5
RayCount=25000

[Detector]
type=Surface
Position=100 0 0
Direction=-1 0 0
Up=0 0 1
Width=30
Height=30

[Segment]
type=Elements
PillowType=Pillow

[BaseSurface]
type=Paraboloid
CenterPoint=50 0 0
FocalDistance=25
Axis=1 0 0
Diameter=40

[Grid]
type=Rectangular
StartPoint=50 0 0
Uextent=-15 15
UCellCount=15
Vextent=-15 15
VCellCount=15
Distance=Projected

[Element]
type=Pillow
FocalDistance=25
FocalPoint=0 0 0
upos=0 1
vpos=0 1
usize=2
vsize=2
udepth=0.5
vdepth=0.5
