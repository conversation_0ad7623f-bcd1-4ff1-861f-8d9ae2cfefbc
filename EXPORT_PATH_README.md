# Export Path Configuration

This document explains how to configure the export path for IGS, BGF, and STP files using XML configuration.

## Overview

The application now supports specifying a custom export path for geometry files (IGS, BGF, STP) through XML configuration. This allows you to control where exported files are saved without modifying the code.

## Configuration

To set the export path, add an `<ExportPath>` element to your XML configuration file:

```xml
<Application>
  <ExportPath>C:/Exports</ExportPath>
  <!-- Other configuration elements -->
</Application>
```

### Example

```xml
<?xml version="1.0" encoding="UTF-8"?>
<Application>
  <!-- Set the export path for IGS, BGF, and STP files -->
  <ExportPath>C:/Exports</ExportPath>
  
  <!-- Set which file types to export (igs, bgf, stp) -->
  <ExportType>igs</ExportType>
  
  <!-- Other application configuration -->
</Application>
```

## Path Format

- You can use either forward slashes (`/`) or backslashes (`\`) in the path
- The path can be absolute (e.g., `C:/Exports`) or relative to the application's working directory (e.g., `./exports`)
- **Do not enclose the path in quotes** - the XML parser will handle the path correctly without quotes
- If the specified directory doesn't exist, you may need to create it before running the application

## Notes

- If the `<ExportPath>` element is not specified or is empty, the application will use the default behavior (exporting to the current working directory)
- The application will automatically append the appropriate file extension (`.igs`, `.bgf`, or `.stp`) to the exported files

## Example Configuration File

A complete example configuration file is provided in `export_path_example.xml`.