#include <iostream>
#include <QApplication>
#include <QString>
#include "OPTONIT_temp/Core/CDF.hpp"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    std::cout << "=== BaseSurface XML Parser Test ===" << std::endl;
    
    // Create CDF instance
    CDF cdf;
    
    // Load the test XML file
    QString xmlFile = "basesurface_test.xml";
    std::cout << "Loading XML file: " << xmlFile.toStdString() << std::endl;
    
    int result = cdf.Read(xmlFile);
    
    if (result) {
        std::cout << "✓ XML file loaded successfully!" << std::endl;
        std::cout << std::endl;
        
        // Print the parsed CDF structure
        std::cout << "=== Parsed CDF Structure ===" << std::endl;
        QString cdfOutput = cdf.Print(CDF::PT_LABEL | CDF::PT_DATA);
        std::cout << cdfOutput.toStdString() << std::endl;
        
        // Check for specific BaseSurface sections
        std::cout << std::endl << "=== BaseSurface Verification ===" << std::endl;
        
        // List of expected BaseSurface types
        QStringList expectedSurfaces;
        expectedSurfaces << "Plane" << "Cylinder" << "Ellipsoid" << "Hyperboloid" 
                        << "Paraboloid" << "FFParaboloid" << "CADImport" 
                        << "Numerical" << "Saddle";
        
        int foundCount = 0;
        foreach (QString surfaceType, expectedSurfaces) {
            QList<CDF*> surfaces = cdf.FindSections("BaseSurface:" + surfaceType);
            if (surfaces.size() > 0) {
                std::cout << "✓ Found " << surfaces.size() << " BaseSurface:" 
                         << surfaceType.toStdString() << " section(s)" << std::endl;
                
                // Print parameters for first surface of this type
                CDF* surface = surfaces.first();
                std::cout << "  Parameters: ";
                for (int i = 0; i < surface->NofParameters(); i++) {
                    CDF::Parameter* param = surface->GetParameter(i);
                    if (param) {
                        std::cout << param->GetLabel().toStdString() << "=" 
                                 << param->GetData().toStdString() << " ";
                    }
                }
                std::cout << std::endl;
                foundCount++;
            } else {
                std::cout << "✗ Missing BaseSurface:" << surfaceType.toStdString() << std::endl;
            }
        }
        
        std::cout << std::endl;
        std::cout << "Summary: Found " << foundCount << " out of " << expectedSurfaces.size() 
                 << " expected BaseSurface types" << std::endl;
        
        // Check Application section
        CDF* appSection = cdf.FindSection("Application:General");
        if (appSection) {
            std::cout << "✓ Application:General section found" << std::endl;
            
            // Check ExportType parameter
            CDF::Parameter* exportType = appSection->FindParameter("ExportType");
            if (exportType) {
                std::cout << "✓ ExportType parameter: " << exportType->GetData().toStdString() << std::endl;
            } else {
                std::cout << "✗ ExportType parameter not found" << std::endl;
            }
        } else {
            std::cout << "✗ Application:General section not found" << std::endl;
        }
        
        // Check Segment sections
        QList<CDF*> segments = cdf.FindSections("Segment:Elements");
        std::cout << "✓ Found " << segments.size() << " Segment:Elements section(s)" << std::endl;
        
    } else {
        std::cout << "✗ Failed to load XML file!" << std::endl;
        
        // Print any error messages
        QString info = cdf.GetInfo("Error: ");
        if (!info.isEmpty()) {
            std::cout << info.toStdString() << std::endl;
        }
        
        return 1;
    }
    
    std::cout << std::endl << "=== Test Complete ===" << std::endl;
    return 0;
}
