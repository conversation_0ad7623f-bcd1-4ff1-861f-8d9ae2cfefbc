Application:Reflector(ti_new)
{
 Segment:Elements
 {
  HorizontalSpread=30;
  VerticalSpread=8;
  LightDistribution=-0.25;
  Correction=0.5;
  Convexity=1;

  Grid:Rectangular
  {
   StartPoint=0 0 0;
   Uextent=-30 30;
   Vextent=-30 30;
   UCellCount=40;
   VCellCount=40;
  }
  
  BaseSurface:Paraboloid
  {
   FocalPoint=0 0 0;
   FocalDistance=4.75;
  }

  Source:Point{Position=0 0 0;}
  Detector:Angular{Direction=1 0 0; Up=0 0 1;}
 
 }
}