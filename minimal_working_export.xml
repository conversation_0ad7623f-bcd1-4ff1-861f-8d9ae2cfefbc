<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
  <Application type="General">
    <ExportType>STP</ExportType>
    <ExportPath>./output/minimal_test</ExportPath>
    
    <!-- Main Source -->
    <Source type="Point">
      <Position x="0" y="0" z="-10"/>
    </Source>
    
    <!-- Main Detector -->
    <Detector type="Plane">
      <ReferencePoint x="0" y="0" z="20"/>
    </Detector>
    
    <!-- Minimal working Segment -->
    <Segment name="MinimalTest" type="Elements">
      
      <!-- Segment Source -->
      <Source type="Point">
        <Position x="0" y="0" z="-5"/>
      </Source>
      
      <!-- Segment Detector -->
      <Detector type="Plane">
        <ReferencePoint x="0" y="0" z="15"/>
      </Detector>
      
      <!-- Simple Plane BaseSurface -->
      <BaseSurface type="Plane">
        <ReferencePoint x="0" y="0" z="0"/>
        <Normal x="0" y="0" z="1"/>
      </BaseSurface>
      
      <!-- Simple Grid -->
      <Grid type="Rectangular">
        <StartPoint x="-5" y="-5" z="0"/>
        <Uextent u="-5" v="5"/>
        <Vextent u="-5" v="5"/>
        <UCellCount>5</UCellCount>
        <VCellCount>5</VCellCount>
      </Grid>
      
      <!-- Required Segment parameters -->
      <HorizontalSpread>10.0</HorizontalSpread>
      <VerticalSpread>10.0</VerticalSpread>
      <LightDistribution>Lambertian</LightDistribution>
      <Correction>1.0</Correction>
      <Convexity>1</Convexity>
      
      <!-- Simple Element -->
      <Element type="Pillow">
        <FocalDistance>15.0</FocalDistance>
        <FocalPoint x="0" y="0" z="15"/>
        <usize>2.0</usize>
        <vsize>2.0</vsize>
        
        <!-- Element Source -->
        <Source type="Point">
          <Position x="0" y="0" z="-1"/>
        </Source>
        
        <!-- Element Detector -->
        <Detector type="Plane">
          <ReferencePoint x="0" y="0" z="10"/>
        </Detector>
      </Element>
      
    </Segment>
    
  </Application>
</Configuration>
