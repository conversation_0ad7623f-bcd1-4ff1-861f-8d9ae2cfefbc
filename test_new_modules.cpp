#include <QCoreApplication>
#include <QDebug>
#include <iostream>
#include "OPTONIT_temp/Core/CDF.hpp"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    std::cout << "Testing XML parsing with new SpaceGrid and Pipe modules..." << std::endl;
    
    // Test loading XML file with SpaceGrid and Pipe
    CDF config("ROOT");

    std::cout << "Attempting to read test_spacegrid_pipe.xml..." << std::endl;
    if (config.Read("test_spacegrid_pipe.xml")) {
        std::cout << "Successfully loaded XML file!" << std::endl;
        
        // Print the parsed structure
        QString output = config.Print();
        std::cout << "Parsed structure:" << std::endl;
        std::cout << output.toStdString() << std::endl;
        
        // Test finding sections
        CDF* appSection = config.FindSection("Application");
        if (appSection) {
            std::cout << "\nFound Application section: " << appSection->GetLabel().toStdString() << std::endl;
            
            // Test SpaceGrid section
            CDF* spaceGridSection = appSection->FindSection("SpaceGrid");
            if (spaceGridSection) {
                std::cout << "Found SpaceGrid section: " << spaceGridSection->GetLabel().toStdString() << std::endl;
                
                // Test SpaceGrid parameters
                CDF::Parameter* originParam = spaceGridSection->FindParameter("Origin");
                if (originParam) {
                    std::cout << "SpaceGrid Origin = " << originParam->GetData().toStdString() << std::endl;
                }
                
                CDF::Parameter* uCountParam = spaceGridSection->FindParameter("UCount");
                if (uCountParam) {
                    std::cout << "SpaceGrid UCount = " << uCountParam->GetData().toStdString() << std::endl;
                }
            } else {
                std::cout << "SpaceGrid section not found!" << std::endl;
            }
            
            // Test Pipe section
            CDF* pipeSection = appSection->FindSection("Pipe");
            if (pipeSection) {
                std::cout << "Found Pipe section: " << pipeSection->GetLabel().toStdString() << std::endl;
                
                // Test Pipe parameters
                CDF::Parameter* radiusParam = pipeSection->FindParameter("PipeRadius");
                if (radiusParam) {
                    std::cout << "Pipe Radius = " << radiusParam->GetData().toStdString() << std::endl;
                }
                
                CDF::Parameter* lengthParam = pipeSection->FindParameter("PipeLength");
                if (lengthParam) {
                    std::cout << "Pipe Length = " << lengthParam->GetData().toStdString() << std::endl;
                }
                
                // Test nested LineEntity
                CDF* lineEntitySection = pipeSection->FindSection("LineEntity");
                if (lineEntitySection) {
                    std::cout << "Found nested LineEntity in Pipe: " << lineEntitySection->GetLabel().toStdString() << std::endl;
                }
            } else {
                std::cout << "Pipe section not found!" << std::endl;
            }
            
            // Test Segment with nested SpaceGrid and Pipe
            CDF* segmentSection = appSection->FindSection("Segment");
            if (segmentSection) {
                std::cout << "Found Segment section: " << segmentSection->GetLabel().toStdString() << std::endl;
                
                CDF* nestedSpaceGrid = segmentSection->FindSection("SpaceGrid");
                if (nestedSpaceGrid) {
                    std::cout << "Found nested SpaceGrid in Segment: " << nestedSpaceGrid->GetLabel().toStdString() << std::endl;
                }
                
                CDF* nestedPipe = segmentSection->FindSection("Pipe");
                if (nestedPipe) {
                    std::cout << "Found nested Pipe in Segment: " << nestedPipe->GetLabel().toStdString() << std::endl;
                }
            }
        }
        
    } else {
        std::cout << "Failed to load XML file!" << std::endl;
        QStringList info = config.GetInfo();
        for (int i = 0; i < info.size(); ++i) {
            std::cout << "Error: " << info[i].toStdString() << std::endl;
        }
        return 1;
    }
    
    std::cout << "\nTest completed successfully!" << std::endl;
    return 0;
}
