<?xml version="1.0" encoding="UTF-8"?>
<Configuration version="1.0">
  <Application name="ti_new" type="Reflector">
    <!-- Export path for IGS files -->
    <ExportPath>C:\Users\<USER>\Desktop\optonit_output</ExportPath>
    <!-- Export type (igs, bgf, stp) -->
    <ExportType>igs</ExportType>
    
    <Segment name="Elements" type="Elements">
      <HorizontalSpread>30</HorizontalSpread>
      <VerticalSpread>8</VerticalSpread>
      <LightDistribution>-0.25</LightDistribution>
      <Correction>0.5</Correction>
      <Convexity>1</Convexity>
      
      <Grid type="Rectangular">
        <StartPoint x="0" y="0" z="0"/>
        <Uextent u="-30" v="30"/>
        <Vextent u="-30" v="30"/>
        <UCellCount>40</UCellCount>
        <VCellCount>40</VCellCount>
      </Grid>
      
      <BaseSurface type="Paraboloid">
        <FocalPoint x="0" y="0" z="0"/>
        <FocalDistance>4.75</FocalDistance>
      </BaseSurface>
      
      <Source type="Point">
        <Position x="0" y="0" z="0"/>
      </Source>
      
      <Detector type="Angular">
        <Direction x="1" y="0" z="0"/>
        <Up x="0" y="0" z="1"/>
      </Detector>
    </Segment>
  </Application>
</Configuration>
