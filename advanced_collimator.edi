Application:General
{
 ExportType="IGS";

 Source:Point
 {
  Position=0 0 0;
  Direction=1 0 0;
  RayCount=5000;
 }

 Detector:Angular
 {
  Direction=1 0 0;
  Up=0 0 1;
 }

 Optical:AdvCollimator
 {
  ior=1.586;
  CentralDistance=3;
  CentralDiameter=7;
  CentralSpread=0;
  EdgeDistance=0.5;
  FrontDistance=8;
  LensEntryType=Plane;
  LensExitType=Freeform;
  ReflectorDiameter=25;
  ReflectorSpread=0;
  SideEntryType=Plane;
  TIRExitType=Plane;
  Draft=5;
  LensEntryPlaneAngle=0;
  LensExitSpread=0;

  LensEntryType:Plane
  {
   LensEntryPlaneAngle=0;
  }

  LensExitType:Freeform
  {
   LensExitLength=4;
   LensExitSpread=0;
  }

  Source:Point
  {
   Position=0 0 0;
   Direction=1 0 0;
  }

  Detector:Angular
  {
   Direction=1 0 0;
   Up=0 0 1;
  }

  Distribution:HVSpread
  {
   h=0;
   v=0;
  }
 }
}
