<?xml version="1.0" encoding="UTF-8"?>
<Configuration version="1.0">
  <!-- Complete LightGuide Configuration -->
  <!-- LightGuide creates decoupling elements (teeth) on arbitrary surfaces -->

  <Application name="LightGuideDesign" type="General">
    <!-- Export configuration -->
    <ExportType>IGS</ExportType>

    <!-- Main light source -->
    <Source type="Rectangle">
      <Position x="0" y="0" z="0"/>
      <Direction x="1" y="0" z="0"/>
      <Axis1 x="0" y="1" z="0"/>
      <Axis2 x="0" y="0" z="1"/>
      <Size1>10</Size1>
      <Size2>8</Size2>
      <RayCount>10000</RayCount>
    </Source>

    <!-- Output detector -->
    <Detector type="Plane">
      <ReferencePoint x="50" y="0" z="0"/>
      <Normal x="-1" y="0" z="0"/>
    </Detector>

    <!-- LightGuide Optical Engine -->
    <Optical type="LightGuide">

      <!-- MANDATORY PARAMETERS -->

      <!-- Teeth configuration for Synopsys-style lightguide -->
      <TeethType>OnSurface</TeethType>  <!-- Creates angled teeth on the surface -->

      <!-- Optimized angles for light guiding -->
      <TAngle1>30 30</TAngle1>  <!-- First surface angle -->
      <TAngle2>60 60</TAngle2>  <!-- Second surface angle -->

      <!-- Regular spacing for uniform light distribution -->
      <TDistance>1.5 1.5</TDistance>  <!-- Distance between teeth -->

      <!-- Moderate teeth size for effective light guiding -->
      <TSize>35 35</TSize>  <!-- Teeth depth as percentage -->

      <!-- Reference point for the lightguide -->
      <ReferencePoint x="0" y="0" z="0"/>

      <!-- Base surface definition (MANDATORY) - Cylindrical lightguide like Synopsys image -->
      <BaseSurface type="Cylinder" name="Surface">
        <CenterPoint x="0" y="0" z="0"/>
        <Axis>Z</Axis>
        <Radius>2.5</Radius>
        <usize>40.0</usize>
        <vsize>15.708</vsize>
      </BaseSurface>

      <!-- OPTIONAL PARAMETERS -->

      <!-- Export options -->
      <export_solid>on</export_solid>  <!-- Export solid geometry -->
      <export_steps>off</export_steps>  <!-- Export step-by-step geometry -->

      <!-- Teeth offset parameters -->
      <TOffset1>1</TOffset1>  <!-- First offset -->
      <TOffset2>1</TOffset2>  <!-- Second offset (optional) -->

      <!-- Optical direction (along tube axis) -->
      <OpticalDir x="0" y="0" z="1"/>

      <!-- Material thickness -->
      <Thickness>2.0</Thickness>

      <!-- Detector offset -->
      <DetectorOffset>0.1</DetectorOffset>

      <!-- Export detector geometry -->
      <export_detector>off</export_detector>

    </Optical>
    
  </Application>
</Configuration>
