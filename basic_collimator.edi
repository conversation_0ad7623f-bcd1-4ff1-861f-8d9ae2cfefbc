Application:General
{
 ExportType="IGS";

 Source:Point
 {
  Position=0 0 0;
  Direction=1 0 0;
  RayCount=5000;
 }

 Detector:Angular
 {
  Direction=1 0 0;
  Up=0 0 1;
 }

 Optical:Collimator
 {
  ior=1.586;
  CentralDistance=5;
  CentralDiameter=10;
  CentralSpread=0;
  EdgeDistance=1;
  Draft=5;
  ReflectorDiameter=30;
  ReflectorSpread=0;
  FrontType=Plane;
  ReflectorDiameterAuto=off;

  Source:Point
  {
   Position=0 0 0;
   Direction=1 0 0;
  }

  Detector:Angular
  {
   Direction=1 0 0;
   Up=0 0 1;
  }

  Distribution:HVSpread
  {
   h=0;
   v=0;
  }

  FrontType:Plane
  {
   FrontDistance=10;
  }
 }
}
