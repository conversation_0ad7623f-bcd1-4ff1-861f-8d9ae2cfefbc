Application:General(XPillows)
{
  Source:Directional{Direction=1 0 0;}
  Detector:Angular{Direction=1 0 0; Up=0 0 1;}
  Lens:Elements(Pillows_J)
  {
   HorizontalSpread=-35;
   VerticalSpread=-35;
   
   
#HSpreadTable= 0   3,
         0   -35  -35,
         10  -35  -35,
         14  -35  -35,
         20  -28  -28,
         56  -28  -28;
         
#VSpreadTable=  0     3,
         0   -27  -27,
         14  -30  -30,
         20  -37  -37,
         56  -37  -37;

#HRotationTable=   0     3,
         0       -5    -5, 
         7       4.5   4.5,
         8      -4.5  -4.5,
         17      4.5   4.5;


#VRotationTable=   0     6,
         0        0     0,
         7        0     0,
         8        0     0;
         

    InnerPlanePoint=-1400 0 0;
    OuterIGSFile=Support.igs;
    OnOuterSurface=On;
    IndexOfRefraction=1.586;

    LightDistribution= -0.05;     
                
    Staple=Off;
    Stepped=On;
    #StepsInMaterial = On;
    Benevolent=Off;

PillowType=LensXPillow;

Grid:Curves
{
  GridCurveFamily1=path.igs;
  PillowsSize=1;
  *PillowsSizeWeigh=0.7;
  AlignPillows=Off;
  NumberOfPillows=9;
}
PostProcessing
  {
    StapleTolerance=0.05;
x    ExtractingAngle=2;
x    ExtractingDirection=1 0 0;
    ConnectingSurfaces=On;
    ConnectingRatio=1;
    ConnectingDistance=1;
    ConnectingAngle=60;
    
    DraftAngle=1;
  }
}
}