Application:General
{
 ExportType="IGS";

 Source:Point
 {
  Position=0 0 0;
 }

 Detector:Plane
 {
  ReferencePoint=100 0 0;
 }

 Optical:LightPipeBody
 {
  ShapeType=CurvedPipe;
  CoreRadius=2.0;
  CladdingThickness=0.5;
  RefractiveIndex_Core=1.5;
  RefractiveIndex_Cladding=1.4;
  PipeLength=100;
  Number_of_Segments=50;
  ReferencePoint=0 0 0;
  OpticalDir=1 0 0;

  PathEntity:BSpline
  {
   ControlPoints=
   (
    0 0 0,
    20 10 0,
    40 -10 0,
    60 10 0,
    80 0 0,
    100 0 0
   );
  }
 }
}
