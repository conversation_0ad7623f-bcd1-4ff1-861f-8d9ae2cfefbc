<?xml version="1.0" encoding="UTF-8"?>
<Configuration version="1.0">
  <Application name="General" type="XPillows">
    
    <Source type="Directional">
      <Direction x="1" y="0" z="0"/>
    </Source>
    
    <Detector type="Angular">
      <Direction x="1" y="0" z="0"/>
      <Up x="0" y="0" z="1"/>
    </Detector>
    
    <Lens name="Pillows_J" type="Elements">
      <HorizontalSpread>35</HorizontalSpread>
      <VerticalSpread>35</VerticalSpread>
      
      <InnerPlanePoint x="-1400" y="0" z="0"/>
      <OuterIGSFile>../Support.igs</OuterIGSFile>
      <OnOuterSurface>On</OnOuterSurface>
      <OnSurface>Outer</OnSurface>
      <IndexOfRefraction>1.586</IndexOfRefraction>
      <LightDistribution>-0.05</LightDistribution>
      <Staple>Off</Staple>
      <Stepped>On</Stepped>
      <Relatively>Off</Relatively>
      <StepsInMaterial>On</StepsInMaterial>
      <Benevolent>Off</Benevolent>
      <PillowType>LensPillow</PillowType>
      
      <Grid type="Rectangular">
        <StartPoint x="0" y="0" z="0"/>
        <Uextent>-30 30</Uextent>
        <Vextent>-30 30</Vextent>
        <UCellCount>5</UCellCount>
        <VCellCount>5</VCellCount>
      </Grid>
      
      <PostProcessing>
        <StapleTolerance>0.05</StapleTolerance>
        <ConnectingSurfaces>On</ConnectingSurfaces>
        <ConnectingRatio>1</ConnectingRatio>
        <ConnectingDistance>1</ConnectingDistance>
        <ConnectingAngle>60</ConnectingAngle>
        <DraftAngle>1</DraftAngle>
      </PostProcessing>
      
    </Lens>
    
  </Application>
</Configuration>
