<?xml version="1.0" encoding="UTF-8"?>
<Configuration version="1.0">
  <Application name="CurvedLightPipe" type="General">
    <!-- Export path for output files -->
    <ExportPath>C:\Users\<USER>\Desktop\optonit_output</ExportPath>
    <!-- Export type (igs, bgf, stp) -->
    <ExportType>igs</ExportType>

    <!-- Light source -->
    <Source type="Rectangle">
      <Position>0 0 0</Position>
      <Axis1>0 1 0</Axis1>
      <Axis2>0 0 1</Axis2>
      <Size1>3</Size1>
      <Size2>3</Size2>
      <RayCount>5000</RayCount>
    </Source>

    <!-- Light detector -->
    <Detector type="Plane">
      <ReferencePoint>80 0 0</ReferencePoint>
    </Detector>

    <!-- Curved Light Pipe using LightPipeBody for better curved support -->
    <Optical type="LightPipeBody">
      <!-- Shape configuration -->
      <ShapeType>Circular</ShapeType>
      <Number_of_Segments>60</Number_of_Segments>
      <ReferencePoint>0 0 0</ReferencePoint>
      <OpticalDir>1 0 0</OpticalDir>
      
      <!-- Pipe geometry parameters -->
      <PCrossRadius>3 3</PCrossRadius>
      <PTopRadius>3 3</PTopRadius>
      <PSideRadius1>5 5</PSideRadius1>
      <PSideRadius2>5 5</PSideRadius2>
      <PHeight>3 3</PHeight>
      <PdHeight1>0.2 0.1</PdHeight1>
      <PdHeight2>0.2 0.1</PdHeight2>
      <PWidth1>0.8 0.9</PWidth1>
      <PWidth2>0.8 0.9</PWidth2>

      <!-- Curved path definition - S-curve -->
      <LineEntity name="FreeCurve" type="FreeCurve">
        <ControlPoints>
          0 0 0;
          15 0 0;
          30 0 -8;
          45 0 -8;
          60 0 0;
          80 0 0
        </ControlPoints>
        <Degree>3</Degree>
        <Closed>false</Closed>
      </LineEntity>
    </Optical>
    
  </Application>
</Configuration>
