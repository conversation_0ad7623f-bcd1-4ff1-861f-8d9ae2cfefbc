// Cylindrical Surface with Y-axis
Application:General
{
 ExportType="IGS";

 Source:Point
 {
  Position=0 0 0;
  Direction=1 0 0;
  RayCount=1000;
 }

 Detector:Plane
 {
  ReferencePoint=50 0 0;
 }

 Segment:Test
 {
  Size=1000;

  // Cylindrical surface along Y-axis
  BaseSurface:Cylinder
  {
   CenterPoint=25 0 0;    // Center point of the cylinder
   Axis=Y;                // Cylinder axis along Y
   Radius=15;             // Radius of the cylinder
  }

  Grid:Hexagonal
  {
   StartPoint=25 0 0;
   LineSegmentDirection=0 1 0;
   Width=8;
   UCellCount=12;
   VCellCount=12;
  }
 }
}
