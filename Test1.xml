<?xml version="1.0" encoding="UTF-8"?>
<Configuration version="1.0">
  <Application name="Test1_Design" type="Reflector">
    <!-- Export path for output files -->
    <ExportPath>C:\Users\<USER>\Desktop\optonit_output</ExportPath>
    <!-- Export type (igs, bgf, stp) -->
    <ExportType>igs</ExportType>

    <Source type="Cylinder">
      <Position x="0" y="0" z="0"/>
      <Axis x="1" y="0" z="0"/>
      <Length>3.7</Length>
      <Diameter>1.2</Diameter>
    </Source>
    
    <Detector type="Angular">
      <Direction x="1" y="0" z="0"/>
      <Up x="0" y="0" z="1"/>
    </Detector>
    
    <Segment name="Test1_FFStrip" type="FFStrip">
      <!-- Using StartPointPar to match EDI behavior (parabolic calculation) -->
      <StartPointPar>20.8 0 0</StartPointPar>
      <Vextent>-70 70</Vextent>
      <Uextent>-20 20</Uextent>
      <UCellCount>15</UCellCount>

      <!-- Multiple Radius definitions from original (some commented out) -->
      <!-- Original had several commented Radius tables and values -->
      <!-- Using the final uncommented value: -->
      <Radius>-150</Radius>

      <FixedTangent>on</FixedTangent>

      <Source type="Cylinder">
        <Position x="0" y="0" z="0"/>
        <Axis x="1" y="0" z="0"/>
        <Length>3.7</Length>
        <Diameter>1.2</Diameter>
      </Source>

      <Detector type="Angular">
        <Direction x="1" y="0" z="0"/>
        <Up x="0" y="0" z="1"/>
      </Detector>

      <Distribution type="HVSpread">
        <h>0</h>
        <v>0</v>
      </Distribution>
    </Segment>
    
  </Application>
</Configuration>
