<?xml version="1.0" encoding="UTF-8"?>
<Configuration version="1.0">
  <Application name="General">
    <ExportType>IGS</ExportType>

    <Source type="Point">
      <Position x="0" y="0" z="100"/>
      <Direction x="0" y="0" z="-1"/>
      <RayCount>1000</RayCount>
    </Source>

    <Detector type="Plane">
      <ReferencePoint x="0" y="0" z="-100"/>
    </Detector>

    <Segment name="Test" type="Test">
      <Size>1000</Size>

      <BaseSurface type="Ellipsoid">
        <CenterPoint x="0" y="0" z="0"/>
        <SemiaxisA>50</SemiaxisA>
        <SemiaxisB>60</SemiaxisB>
      </BaseSurface>

      <Grid type="Hexagonal">
        <StartPoint x="0" y="0" z="0"/>
        <LineSegmentDirection x="0" y="0" z="1"/>
        <Width>6</Width>
        <UCellCount>10</UCellCount>
        <VCellCount>10</VCellCount>
      </Grid>
    </Segment>
  </Application>
</Configuration>
