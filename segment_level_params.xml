<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
  <Application type="General">
    <ExportType>STP</ExportType>
    <ExportPath>./output/segment_test</ExportPath>
    
    <!-- Main Source -->
    <Source type="Point">
      <Position x="0" y="0" z="-10"/>
    </Source>
    
    <!-- Main Detector -->
    <Detector type="Plane">
      <ReferencePoint x="0" y="0" z="20"/>
    </Detector>
    
    <!-- Segment with parameters at Segment level -->
    <Segment name="PlaneTest" type="Elements">
      
      <!-- Segment Source -->
      <Source type="Point">
        <Position x="0" y="0" z="-5"/>
      </Source>
      
      <!-- Segment Detector -->
      <Detector type="Plane">
        <ReferencePoint x="0" y="0" z="15"/>
      </Detector>
      
      <!-- Plane BaseSurface -->
      <BaseSurface type="Plane" name="TestPlane">
        <ReferencePoint x="0" y="0" z="0"/>
        <Normal x="0" y="0" z="1"/>
        <usize>20.0</usize>
        <vsize>20.0</vsize>
      </BaseSurface>
      
      <!-- Grid -->
      <Grid type="Rectangular">
        <StartPoint x="-10" y="-10" z="0"/>
        <Uextent u="-10" v="10"/>
        <Vextent u="-10" v="10"/>
        <UCellCount>20</UCellCount>
        <VCellCount>20</VCellCount>
      </Grid>
      
      <!-- Segment-level parameters for Element:Pillow -->
      <HorizontalSpread>5.0</HorizontalSpread>
      <VerticalSpread>5.0</VerticalSpread>
      <LightDistribution>Lambertian</LightDistribution>
      <Correction>1.0</Correction>
      <Convexity>1</Convexity>
      
      <!-- Element with minimal parameters -->
      <Element type="Pillow">
        <FocalDistance>10.0</FocalDistance>
        <FocalPoint x="0" y="0" z="10"/>
        <usize>1.0</usize>
        <vsize>1.0</vsize>
        <udepth>0.5</udepth>
        <vdepth>0.5</vdepth>
        
        <!-- Element Source -->
        <Source type="Point">
          <Position x="0" y="0" z="-2"/>
        </Source>
        
        <!-- Element Detector -->
        <Detector type="Plane">
          <ReferencePoint x="0" y="0" z="12"/>
        </Detector>
      </Element>
      
    </Segment>
    
  </Application>
</Configuration>
