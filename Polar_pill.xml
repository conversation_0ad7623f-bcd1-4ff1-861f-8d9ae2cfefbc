<?xml version="1.0" encoding="UTF-8"?>
<Configuration version="1.0">
  <Application name="Reflector_v2" type="Reflector">
    <!-- Export path for output files -->
    <ExportPath>C:\Users\<USER>\Desktop\optonit_output</ExportPath>
    <!-- Export type (igs, bgf, stp) -->
    <ExportType>igs</ExportType>

    <Source type="Point">
      <Position>0 0 0</Position>
    </Source>
    
    <Detector type="Angular">
      <Direction>1 0 0</Direction>
      <Up>0 0 1</Up>
    </Detector>
    
    <Segment name="Rectangular" type="Elements">
      <HorizontalSpread>20</HorizontalSpread>
      <VerticalSpread>10</VerticalSpread>
      <Convexity>1</Convexity>
      <LightDistribution>-0.15 -0.15</LightDistribution>
      <Correction>0.25</Correction>
      
      <Grid type="Polar">
        <Pole>0 0 0</Pole>
        <Rextent>10 60</Rextent>
        <Aextent>0 360</Aextent>
        <RCellCount>8</RCellCount>
        <ACellCount>16</ACellCount>
      </Grid>
      
      <BaseSurface type="Paraboloid">
        <FocalPoint>0 0 0</FocalPoint>
        <FocalDistance>15</FocalDistance>
      </BaseSurface>
    </Segment>
    
  </Application>
</Configuration>
