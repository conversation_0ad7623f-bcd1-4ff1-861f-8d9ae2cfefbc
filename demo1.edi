Application:General
{
 ExportType="IGS";

 Source:Point
 {
  Position=0 0 0;
  Direction=1 0 0;
  RayCount=5000;
 }

 Detector:Angular
 {
  Direction=1 0 0;
  Up=0 0 1;
 }

 Segment:FFStrip
 {
  StartPointPar=20 0 0;
  Uextent=-20 20;
  Vextent=-30 30;
  UCellCount=15;
  VCellCount=15;
  Radius=60;

  FixedTangent=on;

  Source:Point
  {
   Position=0 0 0;
   Direction=1 0 0;
  }

  Detector:Angular
  {
   Direction=1 0 0;
   Up=0 0 1;
  }

  Distribution:HVSpread
  {
   h=0;
   v=0;
  }
 }
}