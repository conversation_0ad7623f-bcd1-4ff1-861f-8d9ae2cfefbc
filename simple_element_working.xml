<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
  <Application type="General">
    <ExportType>STP</ExportType>
    <ExportPath>./output/simple_element_test</ExportPath>
    
    <!-- Main Source -->
    <Source type="Point">
      <Position x="0" y="0" z="-10"/>
    </Source>
    
    <!-- Main Detector -->
    <Detector type="Plane">
      <ReferencePoint x="0" y="0" z="20"/>
    </Detector>
    
    <!-- Simple working Segment -->
    <Segment name="SimpleElementTest" type="Elements">
      
      <!-- Segment Source -->
      <Source type="Point">
        <Position x="0" y="0" z="-5"/>
      </Source>
      
      <!-- Segment Detector -->
      <Detector type="Plane">
        <ReferencePoint x="0" y="0" z="15"/>
      </Detector>
      
      <!-- Plane BaseSurface -->
      <BaseSurface type="Plane" name="TestPlane">
        <ReferencePoint x="0" y="0" z="0"/>
        <Normal x="0" y="0" z="1"/>
        <usize>20.0</usize>
        <vsize>20.0</vsize>
      </BaseSurface>
      
      <!-- Grid -->
      <Grid type="Rectangular">
        <StartPoint x="-10" y="-10" z="0"/>
        <Uextent u="-10" v="10"/>
        <Vextent u="-10" v="10"/>
        <UCellCount>10</UCellCount>
        <VCellCount>10</VCellCount>
      </Grid>
      
      <!-- Required Segment-level parameters for Element:Pillow -->
      <HorizontalSpread>20.0</HorizontalSpread>
      <VerticalSpread>10.0</VerticalSpread>
      <LightDistribution>Lambertian</LightDistribution>
      <Correction>0.25</Correction>
      <Convexity>1</Convexity>



      <!-- Simple Element with minimal parameters -->
      <Element type="Pillow">
        <FocalDistance>15.0</FocalDistance>
        <FocalPoint x="0" y="0" z="15"/>
        <usize>2.0</usize>
        <vsize>2.0</vsize>
        <udepth>0.5</udepth>
        <vdepth>0.5</vdepth>

        

      </Element>
      
    </Segment>
    
  </Application>
</Configuration>
