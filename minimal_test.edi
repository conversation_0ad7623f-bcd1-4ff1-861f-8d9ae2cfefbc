Application:General
{
 ExportType="IGS";

 Source:Point
 {
  Position=0 0 100;
  Direction=0 0 -1;
  RayCount=1000;
 }

 Detector:Plane
 {
  ReferencePoint=0 0 -100;
 }

 Segment:Test
 {
  Size=1000;

  BaseSurface:Ellipsoid
  {
   CenterPoint=0 0 0;
   SemiaxisA=50;
   SemiaxisB=60;
  };

  Grid:Hexagonal
  {
   StartPoint=0 0 0;
   LineSegmentDirection=0 0 1;
   Width=6;
   UCellCount=10;
   VCellCount=10;
  };
 }
}
