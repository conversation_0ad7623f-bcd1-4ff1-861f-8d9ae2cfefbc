<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
  <Application type="General">
    <ExportType>STP</ExportType>
    
    <!-- Simple Source -->
    <Source type="Point">
      <Position x="0" y="0" z="-10"/>
    </Source>
    
    <!-- Simple Detector -->
    <Detector type="Plane">
      <ReferencePoint x="0" y="0" z="20"/>
    </Detector>
    
    <!-- Minimal Segment with Element -->
    <Segment name="ElementTest" type="Elements">

      <Source type="Point">
        <Position x="0" y="0" z="-5"/>
      </Source>

      <Detector type="Plane">
        <ReferencePoint x="0" y="0" z="15"/>
      </Detector>

      <BaseSurface type="Plane">
        <ReferencePoint x="0" y="0" z="0"/>
        <Normal x="0" y="0" z="1"/>
      </BaseSurface>

      <Grid type="Rectangular">
        <StartPoint x="-5" y="-5" z="0"/>
        <Uextent u="-5" v="5"/>
        <Vextent u="-5" v="5"/>
        <UCellCount>10</UCellCount>
        <VCellCount>10</VCellCount>
      </Grid>

      <!-- Segment-level parameters that might be needed for Element -->
      <HorizontalSpread>5.0</HorizontalSpread>
      <VerticalSpread>5.0</VerticalSpread>
      <LightDistribution>Lambertian</LightDistribution>
      <Correction>1.0</Correction>
      <Convexity>1</Convexity>

      <!-- Test Element with required parameters -->
      <Element type="Pillow">
        <FocalDistance>10.0</FocalDistance>
        <FocalPoint x="0" y="0" z="10"/>
        <usize>1.0</usize>
        <vsize>1.0</vsize>
        <udepth>0.5</udepth>
        <vdepth>0.5</vdepth>

        <!-- Element also needs its own spread parameters -->
        <HorizontalSpread>5.0</HorizontalSpread>
        <VerticalSpread>5.0</VerticalSpread>
        <LightDistribution>Lambertian</LightDistribution>
        <Correction>1.0</Correction>
        <Convexity>1</Convexity>

        <Source type="Point">
          <Position x="0" y="0" z="-2"/>
        </Source>

        <Detector type="Plane">
          <ReferencePoint x="0" y="0" z="12"/>
        </Detector>
      </Element>

    </Segment>
    
  </Application>
</Configuration>
