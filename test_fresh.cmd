@echo off
echo Testing EXPOSE with detailed diagnostics...

set QTEXPOSEDEV=D:\S1
set QTDIR=C:\Qt4
set EXPOSE=D:\S1
set PATH=C:\Qt4\bin;%PATH%

echo Environment set:
echo QTEXPOSEDEV=%QTEXPOSEDEV%
echo QTDIR=%QTDIR%
echo EXPOSE=%EXPOSE%

echo.
echo === Test 1: Minimal XML file ===
bin\uoptonit.exe minimal_test.xml > output_minimal.txt 2>&1
echo Exit code: %ERRORLEVEL%

echo.
echo === Test 2: Test.xml with log file ===
bin\uoptonit.exe rectangular_pill.xml test.log > output_test_with_log.txt 2>&1
echo Exit code: %ERRORLEVEL%

echo.
echo === Test 3: LightPipe with export ===
bin\uoptonit.exe lightpipe_design.xml lightpipe.log > output_lightpipe_with_log.txt 2>&1
echo Exit code: %ERRORLEVEL%

echo.
echo === Checking for any generated files ===
dir *.igs *.bgf *.log *.txt 2>nul

echo.
echo === Checking output file contents ===
if exist output_minimal.txt (
    echo --- Minimal test output ---
    type output_minimal.txt
)
if exist output_test_with_log.txt (
    echo --- Test with log output ---
    type output_test_with_log.txt
)
if exist test.log (
    echo --- Test log file ---
    type test.log
)

pause
