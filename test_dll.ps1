# Test DLL loading
$env:QTEXPOSEDEV = "D:\S1"
$env:QTDIR = "C:\Qt4"
$env:EXPOSE = "D:\S1"
$env:PATH = "C:\Qt4\bin;" + $env:PATH

Write-Host "Testing DLL loading..."

try {
    # Try to load the Core.dll
    Add-Type -Path "bin\Core.dll" -ErrorAction Stop
    Write-Host "Core.dll loaded successfully"
} catch {
    Write-Host "Failed to load Core.dll: $($_.Exception.Message)"
}

try {
    # Try to load Qt DLLs
    [System.Reflection.Assembly]::LoadFrom((Resolve-Path "bin\QtCore4.dll").Path)
    Write-Host "QtCore4.dll loaded successfully"
} catch {
    Write-Host "Failed to load QtCore4.dll: $($_.Exception.Message)"
}
