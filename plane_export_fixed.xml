<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
  <Application type="General">
    <ExportType>STP</ExportType>
    <ExportPath>./output/plane_test</ExportPath>

    <!-- Main Source for the application -->
    <Source type="Point">
      <Position x="0" y="0" z="-10"/>
    </Source>
    
    <!-- Main Detector for the application - using ReferencePoint instead of Position -->
    <Detector type="Plane">
      <ReferencePoint x="0" y="0" z="20"/>
      <Direction x="0" y="0" z="-1"/>
      <Width>50</Width>
      <Height>50</Height>
    </Detector>
    
    <!-- Complete Segment with Plane BaseSurface -->
    <Segment name="PlaneSegmentTest" type="Elements">
      
      <!-- Source for this segment -->
      <Source type="Point">
        <Position x="0" y="0" z="-5"/>
      </Source>
      
      <!-- Detector for this segment - using ReferencePoint -->
      <Detector type="Plane">
        <ReferencePoint x="0" y="0" z="15"/>
        <Direction x="0" y="0" z="-1"/>
        <Width>30</Width>
        <Height>30</Height>
      </Detector>
      
      <!-- Plane BaseSurface with all required parameters -->
      <BaseSurface type="Plane" name="TestPlane">
        <ReferencePoint x="0" y="0" z="0"/>
        <Normal x="0" y="0" z="1"/>
        <usize>20.0</usize>
        <vsize>20.0</vsize>
        
        <!-- Surface Transformation with correct Scale format -->
        <SurfaceTransformation>
          <Translation x="0" y="0" z="0"/>
          <Rotation x="0" y="0" z="0"/>
        </SurfaceTransformation>
      </BaseSurface>
      
      <!-- Grid for the segment -->
      <Grid type="Rectangular">
        <StartPoint x="-10" y="-10" z="0"/>
        <Uextent u="-10" v="10"/>
        <Vextent u="-10" v="10"/>
        <UCellCount>20</UCellCount>
        <VCellCount>20</VCellCount>
      </Grid>
      
      <!-- Element configuration with all required parameters -->
      <Element type="Pillow">
        <FocalDistance>10.0</FocalDistance>
        <FocalPoint x="0" y="0" z="10"/>
        <usize>1.0</usize>
        <vsize>1.0</vsize>
        <udepth>0.5</udepth>
        <vdepth>0.5</vdepth>

        <!-- Required spread parameters -->
        <HorizontalSpread>5.0</HorizontalSpread>
        <VerticalSpread>5.0</VerticalSpread>

        <!-- Required Element parameters -->
        <LightDistribution>Lambertian</LightDistribution>
        <Correction>1.0</Correction>
        <Convexity>1</Convexity>

        <!-- Source for element -->
        <Source type="Point">
          <Position x="0" y="0" z="-2"/>
        </Source>

        <!-- Detector for element - using ReferencePoint -->
        <Detector type="Plane">
          <ReferencePoint x="0" y="0" z="12"/>
          <Direction x="0" y="0" z="-1"/>
          <Width>5</Width>
          <Height>5</Height>
        </Detector>
      </Element>
      
      <!-- Distribution for light -->
      <Distribution type="HVSpread">
        <h>5.0</h>
        <v>5.0</v>
        <w>0.0</w>
      </Distribution>
      
    </Segment>
    
    <!-- PostProcessing configuration -->
    <PostProcessing>
      <StapleTolerance>0.1</StapleTolerance>
      <ExtractingAngle>45.0</ExtractingAngle>
      <ExtractingDirection x="0" y="0" z="1"/>
      <ConnectingSurfaces>true</ConnectingSurfaces>
      <ConnectingRatio>0.8</ConnectingRatio>
      <ConnectingDistance>0.5</ConnectingDistance>
      <ConnectingAngle>30.0</ConnectingAngle>
      <DraftAngle>2.0</DraftAngle>
    </PostProcessing>
    
  </Application>
</Configuration>
