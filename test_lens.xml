<?xml version="1.0" encoding="UTF-8"?>
<Configuration version="1.0">
  <Application name="General">
    <Source type="Directional">
      <Direction x="1" y="0" z="0"/>
    </Source>
    
    <Detector type="Angular">
      <Direction x="1" y="0" z="0"/>
      <Up x="0" y="0" z="1"/>
    </Detector>
    
    <Lens name="LensPillow" type="Elements">
      <Color>red</Color>
      <InnerPlanePoint x="0" y="0" z="0"/>
      <OuterPlanePoint x="2" y="0" z="0"/>
      <HSpreadTable>20</HSpreadTable>
      <VSpreadTable>10</VSpreadTable>
      <LightDistribution>-0.15</LightDistribution>
      <IndexOfRefraction>1.586</IndexOfRefraction>
      <Relatively>On</Relatively>
      <OnOuterSurface>On</OnOuterSurface>
      <ConnectingSurfaces>Off</ConnectingSurfaces>
      <ConnectingRatio>0.2</ConnectingRatio>
      <Benevolent>On</Benevolent>
      <Stepped>Off</Stepped>

      <Grid type="UVCurves">
        <IGSFile>GRID1.igs</IGSFile>
        <Trimming>Off</Trimming>
        <Distance>OnSurface</Distance>
        <UCellCount>53</UCellCount>
        <VCellCount>19</VCellCount>
      </Grid>
    </Lens>
  </Application>
</Configuration>
