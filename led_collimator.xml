<?xml version="1.0" encoding="UTF-8"?>
<Configuration version="1.0">
  <Application name="LEDCollimator" type="General">
    <!-- Export path for output files -->
    <ExportPath>C:\Users\<USER>\Desktop\optonit_output</ExportPath>
    <!-- Export type (igs, bgf, stp) -->
    <ExportType>igs</ExportType>

    <!-- Main Source definition -->
    <Source type="Cylinder">
      <Position>0 0 0</Position>
      <Axis>1 0 0</Axis>
      <Length>1</Length>
      <Diameter>3</Diameter>
      <RayCount>15000</RayCount>
    </Source>

    <!-- Main Detector definition -->
    <Detector type="Angular">
      <Direction>1 0 0</Direction>
      <Up>0 0 1</Up>
    </Detector>

    <!-- Advanced Collimator Optical Engine -->
    <Optical type="AdvCollimator">
      <!-- Material properties -->
      <ior>1.586</ior>
      
      <!-- Geometric parameters -->
      <CentralDistance>4</CentralDistance>
      <CentralDiameter>8</CentralDiameter>
      <CentralSpread>0</CentralSpread>
      <EdgeDistance>1</EdgeDistance>
      <FrontDistance>12</FrontDistance>
      <ReflectorDiameter>28</ReflectorDiameter>
      <ReflectorSpread>0</ReflectorSpread>
      <LensExitSpread>0</LensExitSpread>
      <Draft>5</Draft>
      
      <!-- Lens configuration -->
      <LensEntryType>Spherical</LensEntryType>
      <LensExitType>Freeform</LensExitType>
      <SideEntryType>Plane</SideEntryType>
      <TIRExitType>Plane</TIRExitType>
      <LensEntryFocalPointLateralShift>0</LensEntryFocalPointLateralShift>
      <LensEntryFocalPointLongitudinalShift>-2</LensEntryFocalPointLongitudinalShift>

      <!-- Nested sections for lens types (matching EDI structure) -->
      <LensEntryType name="Spherical" type="Spherical">
        <LensEntryFocalPointLateralShift>0</LensEntryFocalPointLateralShift>
        <LensEntryFocalPointLongitudinalShift>-2</LensEntryFocalPointLongitudinalShift>
      </LensEntryType>

      <LensExitType name="Freeform" type="Freeform">
        <LensExitLength>6</LensExitLength>
      </LensExitType>

      <!-- Source configuration for the optical engine -->
      <Source type="Cylinder">
        <Position>0 0 0</Position>
        <Axis>1 0 0</Axis>
        <Length>1</Length>
        <Diameter>3</Diameter>
      </Source>

      <!-- Detector configuration for the optical engine -->
      <Detector type="Angular">
        <Direction>1 0 0</Direction>
        <Up>0 0 1</Up>
      </Detector>

      <!-- Distribution configuration -->
      <Distribution type="HVSpread">
        <h>0</h>
        <v>0</v>
      </Distribution>
    </Optical>
    
  </Application>
</Configuration>
