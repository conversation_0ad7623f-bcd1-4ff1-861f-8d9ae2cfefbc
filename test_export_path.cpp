#include <QCoreApplication>
#include <QDebug>
#include <iostream>
#include "OPTONIT_temp/Core/CDF.hpp"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    std::cout << "Testing ExportPath parsing with rectangular_pill.xml..." << std::endl;
    
    // Test loading XML file
    CDF config("ROOT");

    std::cout << "Attempting to read rectangular_pill.xml..." << std::endl;
    if (config.Read("rectangular_pill.xml")) {
        std::cout << "Successfully loaded XML file!" << std::endl;
        
        // Print the parsed structure
        QString output = config.Print();
        std::cout << "Parsed structure:" << std::endl;
        std::cout << output.toStdString() << std::endl;
        
        // Test finding Application section
        CDF* appSection = config.FindSection("Application");
        if (appSection) {
            std::cout << "Found Application section: " << appSection->GetLabel().toStdString() << std::endl;
            
            // Test ExportPath parameter access
            CDF::Parameter* exportPathParam = appSection->FindParameter("ExportPath");
            if (exportPathParam) {
                std::cout << "ExportPath = " << exportPathParam->GetData().toStdString() << std::endl;
            } else {
                std::cout << "ExportPath parameter not found!" << std::endl;
            }
            
            // Test ExportType parameter access
            CDF::Parameter* exportTypeParam = appSection->FindParameter("ExportType");
            if (exportTypeParam) {
                std::cout << "ExportType = " << exportTypeParam->GetData().toStdString() << std::endl;
            } else {
                std::cout << "ExportType parameter not found!" << std::endl;
            }
        } else {
            std::cout << "Application section not found!" << std::endl;
        }
        
    } else {
        std::cout << "Failed to load XML file!" << std::endl;
        QStringList info = config.GetInfo();
        for (int i = 0; i < info.size(); ++i) {
            std::cout << "Error: " << info[i].toStdString() << std::endl;
        }
        return 1;
    }
    
    return 0;
}
