<?xml version="1.0" encoding="UTF-8"?>
<Configuration version="1.0">
  <!-- Comprehensive Light Guide with Optical Elements -->
  <Application name="General">
    <ExportType>IGS</ExportType>

    <Source type="Rectangle">
      <Position x="0" y="0" z="0"/>
      <Direction x="1" y="0" z="0"/>
      <Axis1 x="0" y="1" z="0"/>
      <Axis2 x="0" y="0" z="1"/>
      <Size1>5</Size1>
      <Size2>5</Size2>
      <RayCount>25000</RayCount>
    </Source>

    <Detector type="Plane">
      <ReferencePoint x="100" y="0" z="0"/>
      <Normal x="-1" y="0" z="0"/>
    </Detector>

    <Segment type="Elements">
      <PillowType>Pillow</PillowType>

      <Source type="Rectangle">
        <Position x="0" y="0" z="0"/>
        <Direction x="1" y="0" z="0"/>
        <Axis1 x="0" y="1" z="0"/>
        <Axis2 x="0" y="0" z="1"/>
        <Size1>5</Size1>
        <Size2>5</Size2>
      </Source>

      <Detector type="Plane">
        <ReferencePoint x="100" y="0" z="0"/>
        <Normal x="-1" y="0" z="0"/>
      </Detector>

      <BaseSurface type="Paraboloid">
        <CenterPoint x="50" y="0" z="0"/>
        <FocalDistance>25</FocalDistance>
        <Axis x="1" y="0" z="0"/>
        <Diameter>40</Diameter>
      </BaseSurface>

      <Grid type="Rectangular">
        <StartPoint x="50" y="0" z="0"/>
        <Uextent u="-15" v="15"/>
        <UCellCount>15</UCellCount>
        <Vextent u="-15" v="15"/>
        <VCellCount>15</VCellCount>
        <Distance>Projected</Distance>
      </Grid>

      <Element type="Pillow">
        <FocalDistance>25</FocalDistance>
        <FocalPoint x="0" y="0" z="0"/>
        <HorizontalSpread>10</HorizontalSpread>
        <VerticalSpread>10</VerticalSpread>
        <LightDistribution>-0.15</LightDistribution>
        <Correction>0.5</Correction>
        <Convexity>1</Convexity>
        <upos>0 1</upos>
        <vpos>0 1</vpos>
        <usize>2</usize>
        <vsize>2</vsize>
        <udepth>0.5</udepth>
        <vdepth>0.5</vdepth>
      </Element>
    </Segment>
  </Application>
</Configuration>
