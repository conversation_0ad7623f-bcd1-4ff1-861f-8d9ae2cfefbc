<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
  <Application type="General">
    <ExportType>STP</ExportType>
    <ExportPath>./output/</ExportPath>
    
    <!-- Test all BaseSurface types -->
    
    <!-- 1. Plane BaseSurface -->
    <Segment name="PlaneTest" type="Elements">
      <BaseSurface type="Plane" name="TestPlane">
        <ReferencePoint x="0" y="0" z="0"/>
        <Normal x="0" y="0" z="1"/>
        <usize>10.0</usize>
        <vsize>10.0</vsize>
        
        <Grid type="Rectangular">
          <Uextent u="5.0" v="5.0"/>
          <Vextent u="5.0" v="5.0"/>
          <Ucount>10</Ucount>
          <Vcount>10</Vcount>
        </Grid>
        
        <SurfaceTransformation>
          <Translation x="0" y="0" z="5"/>
          <Rotation x="0" y="0" z="0"/>
        </SurfaceTransformation>
      </BaseSurface>
    </Segment>
    
    <!-- 2. <PERSON>linder BaseSurface -->
    <Segment name="CylinderTest" type="Elements">
      <BaseSurface type="Cylinder" name="TestCylinder">
        <CenterPoint x="10" y="0" z="0"/>
        <Axis>Z</Axis>
        <Radius>5.0</Radius>
        <usize>8.0</usize>
        <vsize>12.0</vsize>
        
        <SpaceGrid type="Cylindrical">
          <Uextent u="3.14" v="6.28"/>
          <Vextent u="0" v="10"/>
          <Ucount>20</Ucount>
          <Vcount>15</Vcount>
        </SpaceGrid>
      </BaseSurface>
    </Segment>
    
    <!-- 3. Ellipsoid BaseSurface -->
    <Segment name="EllipsoidTest" type="Elements">
      <BaseSurface type="Ellipsoid" name="TestEllipsoid">
        <CenterPoint x="20" y="0" z="0"/>
        <SemiaxisA>8.0</SemiaxisA>
        <SemiaxisB>6.0</SemiaxisB>
        <SemiaxisC>4.0</SemiaxisC>
        <usize>12.0</usize>
        <vsize>8.0</vsize>
      </BaseSurface>
    </Segment>
    
    <!-- 4. Hyperboloid BaseSurface -->
    <Segment name="HyperboloidTest" type="Elements">
      <BaseSurface type="Hyperboloid" name="TestHyperboloid">
        <CenterPoint x="30" y="0" z="0"/>
        <SemiaxisA>5.0</SemiaxisA>
        <SemiaxisB>3.0</SemiaxisB>
        <usize>10.0</usize>
        <vsize>8.0</vsize>
      </BaseSurface>
    </Segment>
    
    <!-- 5. Paraboloid BaseSurface -->
    <Segment name="ParaboloidTest" type="Elements">
      <BaseSurface type="Paraboloid" name="TestParaboloid">
        <FocalPoint x="40" y="0" z="0"/>
        <Axis x="0" y="0" z="1"/>
        <FocalDistance>15.0</FocalDistance>
        <usize>12.0</usize>
        <vsize>12.0</vsize>
      </BaseSurface>
    </Segment>
    
    <!-- 6. FFParaboloid BaseSurface -->
    <Segment name="FFParaboloidTest" type="Elements">
      <BaseSurface type="FFParaboloid" name="TestFFParaboloid">
        <focal_point x="50" y="0" z="0"/>
        <axis>Y</axis>
        <focal_distance>20.0</focal_distance>
        <radius>8.0</radius>
        <usize>10.0</usize>
        <vsize>10.0</vsize>
      </BaseSurface>
    </Segment>
    
    <!-- 7. CADImport BaseSurface with IGS -->
    <Segment name="CADImportIGSTest" type="Elements">
      <BaseSurface type="CADImport" name="TestCADIGS">
        <IGSFile>./models/surface.igs</IGSFile>
        <Trimming>true</Trimming>
        <usize>15.0</usize>
        <vsize>15.0</vsize>
      </BaseSurface>
    </Segment>
    
    <!-- 8. CADImport BaseSurface with BGF -->
    <Segment name="CADImportBGFTest" type="Elements">
      <BaseSurface type="CADImport" name="TestCADBGF">
        <BGFFile>./models/surface.bgf</BGFFile>
        <Trimming>false</Trimming>
        <usize>12.0</usize>
        <vsize>12.0</vsize>
      </BaseSurface>
    </Segment>
    
    <!-- 9. CADImport BaseSurface with STP -->
    <Segment name="CADImportSTPTest" type="Elements">
      <BaseSurface type="CADImport" name="TestCADSTP">
        <STPFile>./models/surface.stp</STPFile>
        <Trimming>true</Trimming>
        <usize>18.0</usize>
        <vsize>18.0</vsize>
      </BaseSurface>
    </Segment>
    
    <!-- 10. Numerical BaseSurface -->
    <Segment name="NumericalTest" type="Elements">
      <BaseSurface type="Numerical" name="TestNumerical">
        <usize>10.0</usize>
        <vsize>10.0</vsize>
        
        <!-- Numerical BaseSurface contains a Segment -->
        <Segment name="NumericalSegment" type="FFStrip">
          <StartPoint x="0" y="0" z="0"/>
          <Uextent>5.0</Uextent>
          <Vextent>5.0</Vextent>
          <UCellCount>20</UCellCount>
          <VCellCount>20</VCellCount>
          <Radius>25.0</Radius>
          <FixedTangent>true</FixedTangent>
        </Segment>
      </BaseSurface>
    </Segment>
    
    <!-- 11. Saddle BaseSurface -->
    <Segment name="SaddleTest" type="Elements">
      <BaseSurface type="Saddle" name="TestSaddle">
        <Vertex x="60" y="0" z="0"/>
        <SemiaxisA>6.0</SemiaxisA>
        <SemiaxisB>4.0</SemiaxisB>
        <usize>8.0</usize>
        <vsize>8.0</vsize>
      </BaseSurface>
    </Segment>
    
  </Application>
</Configuration>
