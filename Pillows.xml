<?xml version="1.0" encoding="UTF-8"?>
<Configuration version="1.0">
  <Application name="XPillows" type="General">
    <!-- Export path for output files -->
    <ExportPath>C:\Users\<USER>\Desktop\optonit_output</ExportPath>
    <!-- Export type (igs, bgf, stp) -->
    <ExportType>igs</ExportType>

    <Source type="Directional">
      <Direction>1 0 0</Direction>
    </Source>

    <Detector type="Angular">
      <Direction>1 0 0</Direction>
      <Up>0 0 1</Up>
    </Detector>
    
    <Lens name="Pillows_J" type="Elements">
      <!-- Spread parameters (commented out in EDI, using simple values) -->
      <HorizontalSpread>-35</HorizontalSpread>
      <VerticalSpread>-35</VerticalSpread>

      <!-- Required parameters from EDI -->
      <InnerPlanePoint>-1400 0 0</InnerPlanePoint>
      <OuterIGSFile>../Support.igs</OuterIGSFile>
      <OnOuterSurface>On</OnOuterSurface>
      <OnSurface>Outer</OnSurface>
      <IndexOfRefraction>1.586</IndexOfRefraction>
      <LightDistribution>-0.05</LightDistribution>
      <Staple>Off</Staple>
      <Stepped>On</Stepped>
      <Benevolent>Off</Benevolent>
      <PillowType>LensXPillow</PillowType>

      <!-- Tables in correct ParamTable format: row_min row_max, col1 val1_row_min val1_row_max, col2 val2_row_min val2_row_max, ... -->
      <HSpreadTable>0 3, 0 -35 -35, 10 -35 -35, 14 -35 -35, 20 -28 -28, 56 -28 -28</HSpreadTable>
      <VSpreadTable>0 3, 0 -27 -27, 14 -30 -30, 20 -37 -37, 56 -37 -37</VSpreadTable>

      <!-- Rotation tables with correct format -->
      <HRotationTable>0 3, 0 -5 -5, 7 4.5 4.5, 8 -4.5 -4.5, 17 4.5 4.5</HRotationTable>
      <VRotationTable>0 6, 0 0 0, 7 0 0, 8 0 0</VRotationTable>

      <!-- Use Curves grid as in original EDI -->
      <Grid type="Curves">
        <StartPoint>0 0 0</StartPoint>
        <GridCurveFamily1>../path.igs</GridCurveFamily1>
        <PillowsSize>1</PillowsSize>
        <AlignPillows>Off</AlignPillows>
        <NumberOfPillows>9</NumberOfPillows>
      </Grid>



      <PostProcessing>
        <StapleTolerance>0.05</StapleTolerance>
        <!-- ExtractingAngle and ExtractingDirection commented out in original -->
        <!-- <ExtractingAngle>2</ExtractingAngle> -->
        <!-- <ExtractingDirection x="1" y="0" z="0"/> -->
        <ConnectingSurfaces>On</ConnectingSurfaces>
        <ConnectingRatio>1</ConnectingRatio>
        <ConnectingDistance>1</ConnectingDistance>
        <ConnectingAngle>60</ConnectingAngle>
        <DraftAngle>1</DraftAngle>
      </PostProcessing>



    </Lens>
    
  </Application>
</Configuration>
