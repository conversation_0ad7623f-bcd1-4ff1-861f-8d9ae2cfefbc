Application:Reflector(Reflector_v2)
{
  Source:Point{Position=0 0 0;}
  Detector:Angular{Direction=1 0 0; Up=0 0 1;}
              
  Segment:Elements(Rectangular)
  {
    HorizontalSpread=20;
    VerticalSpread=10;
    Convexity=1;
    LightDistribution=-0.15 -0.15;
    Correction=0.25;
    
    Grid:Rectangular
    {
      StartPoint=0 0 0;
      Uextent=-60 60;
      Vextent=-60 60;
      UCellCount=20;
      VCellCount=20;
      Distance=Projected;
    }
    
    BaseSurface:Paraboloid
    {
      FocalPoint=0 0 0;
      FocalDistance=15;
    }
  }
}