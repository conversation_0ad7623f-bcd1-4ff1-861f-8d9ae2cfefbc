<?xml version="1.0" encoding="UTF-8"?>
<Configuration version="1.0">
  <Application name="General">
    <ExportType>IGS BGF STP</ExportType>
    
    <!-- Light source - typically rectangular for lightguides -->
    <Source type="Rectangle">
      <Position x="0" y="0" z="0"/>
      <Direction x="1" y="0" z="0"/>
      <Axis1 x="0" y="1" z="0"/>
      <Axis2 x="0" y="0" z="1"/>
      <Size1>10</Size1>
      <Size2>10</Size2>
      <RayCount>5000</RayCount>
    </Source>
    
    <!-- Detector - plane detector to capture output -->
    <Detector type="Plane">
      <ReferencePoint x="100" y="0" z="0"/>
      <Normal x="-1" y="0" z="0"/>
    </Detector>
    
    <!-- Main lightguide segment -->
    <Segment type="Elements">
      <StartPoint x="0" y="0" z="0"/>
      <Direction x="1" y="0" z="0"/>
      <Length>100</Length>
      <Width>20</Width>
      <Height>20</Height>
      
      <!-- Light distribution parameters -->
      <HorizontalSpread>15</HorizontalSpread>
      <VerticalSpread>15</VerticalSpread>
      <LightDistribution>-0.1</LightDistribution>
      
      <!-- Material properties -->
      <IndexOfRefraction>1.49</IndexOfRefraction>
      <Relatively>On</Relatively>
      <Stepped>Off</Stepped>
      <Benevolent>On</Benevolent>
      
      <!-- Base surface for the lightguide -->
      <BaseSurface type="Plane">
        <ReferencePoint x="0" y="0" z="-10"/>
        <Normal x="0" y="0" z="1"/>
      </BaseSurface>
      
      <!-- Grid for element placement -->
      <Grid type="Rectangular">
        <StartPoint x="10" y="0" z="0"/>
        <Uextent>-8 8</Uextent>
        <Vextent>-8 8</Vextent>
        <UCellCount>8</UCellCount>
        <VCellCount>8</VCellCount>
      </Grid>
      
      <!-- Light source for segment -->
      <Source type="Rectangle">
        <Position x="0" y="0" z="0"/>
        <Direction x="1" y="0" z="0"/>
        <Axis1 x="0" y="1" z="0"/>
        <Axis2 x="0" y="0" z="1"/>
        <Size1>10</Size1>
        <Size2>10</Size2>
      </Source>
      
      <!-- Detector for segment -->
      <Detector type="Plane">
        <ReferencePoint x="100" y="0" z="0"/>
        <Normal x="-1" y="0" z="0"/>
      </Detector>
      
      <!-- Light distribution -->
      <Distribution type="HVSpread">
        <h>15</h>
        <v>15</v>
      </Distribution>
      
      <!-- Element configuration -->
      <Element type="Pillow">
        <FocalDistance>25</FocalDistance>
        <FocalPoint x="0" y="0" z="0"/>
        <HorizontalSpread>15</HorizontalSpread>
        <VerticalSpread>15</VerticalSpread>
        <LightDistribution>-0.1</LightDistribution>
        <Correction>0.5</Correction>
        <Convexity>1</Convexity>
        <Relatively>On</Relatively>
        <Stepped>Off</Stepped>
        <upos>0 1</upos>
        <vpos>0 1</vpos>
        <usize>2</usize>
        <vsize>2</vsize>
        <udepth>0.5</udepth>
        <vdepth>0.5</vdepth>
      </Element>
      
    </Segment>
    
  </Application>
</Configuration>
