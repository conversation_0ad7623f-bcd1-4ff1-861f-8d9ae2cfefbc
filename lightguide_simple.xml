<?xml version="1.0" encoding="UTF-8"?>
<Configuration version="1.0">
  <!-- Simple LightGuide Example -->
  <!-- Creates a basic lightguide with teeth on a flat surface -->
  
  <Application name="SimpleLightGuide" type="General">
    <ExportType>IGS</ExportType>
    <ExportPath>./output</ExportPath>
    
    <!-- LED source -->
    <Source type="Rectangle">
      <Position x="0" y="0" z="0"/>
      <Direction x="1" y="0" z="0"/>
      <Axis1 x="0" y="1" z="0"/>
      <Axis2 x="0" y="0" z="1"/>
      <Size1>5</Size1>
      <Size2>5</Size2>
      <RayCount>5000</RayCount>
    </Source>
    
    <!-- Screen detector -->
    <Detector type="Plane">
      <ReferencePoint x="30" y="0" z="0"/>
      <Normal x="-1" y="0" z="0"/>
    </Detector>
    
    <!-- Simple LightGuide -->
    <Optical type="LightGuide">
      
      <!-- Basic teeth configuration -->
      <TeethType>OnSurface</TeethType>
      <TAngle1>45 45</TAngle1>
      <TAngle2>45 45</TAngle2>
      <TDistance>1 1</TDistance>
      <TSize>50 50</TSize>
      
      <!-- Reference point -->
      <ReferencePoint x="15" y="0" z="0"/>
      
      <!-- Flat base surface -->
      <BaseSurface type="Plane">
        <ReferencePoint x="15" y="0" z="0"/>
        <Normal x="0" y="0" z="1"/>
        <usize>25.0</usize>
        <vsize>15.0</vsize>
      </BaseSurface>
      
      <!-- Export solid geometry -->
      <export_solid>on</export_solid>
      <export_steps>off</export_steps>
      
      <!-- Material properties -->
      <OpticalDir x="1" y="0" z="0"/>
      <Thickness>2.0</Thickness>
      
    </Optical>
    
  </Application>
</Configuration>
