<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
  <Application type="General">
    <ExportType>STP</ExportType>
    <ExportPath>./output/element_table_test</ExportPath>
    
    <!-- Main Source -->
    <Source type="Point">
      <Position x="0" y="0" z="-10"/>
    </Source>
    
    <!-- Main Detector -->
    <Detector type="Plane">
      <ReferencePoint x="0" y="0" z="20"/>
    </Detector>
    
    <!-- Test Segment with ParamTable Elements -->
    <Segment name="ParamTableTest" type="Elements">
      
      <!-- Segment Source -->
      <Source type="Point">
        <Position x="0" y="0" z="-5"/>
      </Source>
      
      <!-- Segment Detector -->
      <Detector type="Plane">
        <ReferencePoint x="0" y="0" z="15"/>
      </Detector>
      
      <!-- Plane BaseSurface -->
      <BaseSurface type="Plane" name="TestPlane">
        <ReferencePoint x="0" y="0" z="0"/>
        <Normal x="0" y="0" z="1"/>
        <usize>20.0</usize>
        <vsize>20.0</vsize>
      </BaseSurface>
      
      <!-- Grid -->
      <Grid type="Rectangular">
        <StartPoint x="-10" y="-10" z="0"/>
        <Uextent u="-10" v="10"/>
        <Vextent u="-10" v="10"/>
        <UCellCount>20</UCellCount>
        <VCellCount>20</VCellCount>
      </Grid>

      <!-- Segment-level parameters for Element:Pillow -->
      <HorizontalSpread>20.0</HorizontalSpread>
      <VerticalSpread>10.0</VerticalSpread>
      <LightDistribution>Lambertian</LightDistribution>
      <Correction>0.25</Correction>
      <Convexity>1</Convexity>

      <!-- Element with ParamTable parameters -->
      <Element type="Pillow">
        <FocalDistance>10.0</FocalDistance>
        <FocalPoint x="0" y="0" z="10"/>
        <usize>1.0</usize>
        <vsize>1.0</vsize>
        <udepth>0.5</udepth>
        <vdepth>0.5</vdepth>
        
        <!-- HorizontalSpread as ParamTable (like your EDI example) -->
        <HorizontalSpread>
          <Header x1="0" x2="3"/>
          <Row y="0" v1="-35" v2="-35"/>
          <Row y="10" v1="-35" v2="-35"/>
          <Row y="14" v1="-35" v2="-35"/>
          <Row y="20" v1="-28" v2="-28"/>
          <Row y="56" v1="-28" v2="-28"/>
        </HorizontalSpread>

        <!-- VerticalSpread as simple value -->
        <VerticalSpread>5.0</VerticalSpread>
        
        <!-- LightDistribution as ParamTable -->
        <LightDistribution>
          <Header x1="0" x2="1"/>
          <Row y="0" v1="1.0" v2="1.0"/>
          <Row y="10" v1="0.8" v2="0.8"/>
          <Row y="20" v1="0.6" v2="0.6"/>
        </LightDistribution>
        
        <!-- Required parameters -->
        <Correction>1.0</Correction>
        <Convexity>1</Convexity>
        
        <!-- Element Source -->
        <Source type="Point">
          <Position x="0" y="0" z="-2"/>
        </Source>
        
        <!-- Element Detector -->
        <Detector type="Plane">
          <ReferencePoint x="0" y="0" z="12"/>
        </Detector>
      </Element>
      
    </Segment>
    
    <!-- Test LensPillow Element with ParamTables -->
    <Segment name="LensPillowTest" type="Elements">
      
      <Source type="Point">
        <Position x="20" y="0" z="-5"/>
      </Source>
      
      <Detector type="Plane">
        <ReferencePoint x="20" y="0" z="15"/>
      </Detector>
      
      <BaseSurface type="Plane">
        <ReferencePoint x="20" y="0" z="0"/>
        <Normal x="0" y="0" z="1"/>
      </BaseSurface>
      
      <Grid type="Rectangular">
        <StartPoint x="15" y="-5" z="0"/>
        <Uextent u="-5" v="5"/>
        <Vextent u="-5" v="5"/>
        <UCellCount>10</UCellCount>
        <VCellCount>10</VCellCount>
      </Grid>
      
      <!-- LensPillow Element with ParamTable format -->
      <Element type="LensPillow">
        
        <!-- HorizontalSpread as ParamTable (LensPillowEngine format) -->
        <HorizontalSpread>
          <Header x1="0" x2="5"/>
          <Row y="0" v1="10" v2="15"/>
          <Row y="5" v1="12" v2="18"/>
          <Row y="10" v1="15" v2="20"/>
        </HorizontalSpread>
        
        <!-- VerticalSpread as ParamTable -->
        <VerticalSpread>
          <Header x1="0" x2="5"/>
          <Row y="0" v1="8" v2="12"/>
          <Row y="5" v1="10" v2="15"/>
          <Row y="10" v1="12" v2="18"/>
        </VerticalSpread>
        
        <!-- LightDistribution as ParamTable -->
        <LightDistribution>
          <Header x1="0" x2="1"/>
          <Row y="0" v1="Lambertian" v2="Lambertian"/>
          <Row y="10" v1="Lambertian" v2="Lambertian"/>
        </LightDistribution>
        
        <!-- Optional parameters -->
        <Relatively>On</Relatively>
        
        <Source type="Point">
          <Position x="20" y="0" z="-2"/>
        </Source>
        
        <Detector type="Plane">
          <ReferencePoint x="20" y="0" z="12"/>
        </Detector>
      </Element>
      
    </Segment>
    
  </Application>
</Configuration>
