// Complex Cylindrical Reflector System
Application:Reflector
{
 ExportType="IGS";

 Source:Cylinder
 {
  Position=-20 0 0;
  Axis=1 0 0;
  Length=5;
  Diameter=3;
 }

 Detector:Angular
 {
  Direction=1 0 0;
  Up=0 0 1;
 }

 Segment:Elements
 {
  PillowType="Pillow";

  // Cylindrical base surface for reflector elements
  BaseSurface:Cylinder
  {
   CenterPoint=0 0 0;
   Axis=Z;
   Radius=20;
  }

  Grid:Rectangular
  {
   StartPoint=0 0 0;
   Uextent=-30 30;
   UCellCount=15;
   Vextent=-15 15;
   VCellCount=10;
   Distance=Projected;
  }

  Element:Pillow
  {
   FocalDistance=15;
   FocalPoint=-20 0 0;
   upos=0 1;
   vpos=0 1;
   usize=3;
   vsize=3;
   udepth=1;
   vdepth=1;
  }
 }
}
