<?xml version="1.0" encoding="UTF-8"?>
<Configuration version="1.0">
  <Application name="SimpleCollimator" type="General">
    <!-- Export path for output files -->
    <ExportPath>C:\Users\<USER>\Desktop\optonit_output</ExportPath>
    <!-- Export type (igs, bgf, stp) -->
    <ExportType>igs</ExportType>

    <!-- Main Source definition -->
    <Source type="Point">
      <Position>0 0 0</Position>
      <Direction>1 0 0</Direction>
      <RayCount>5000</RayCount>
    </Source>

    <!-- Main Detector definition -->
    <Detector type="Angular">
      <Direction>1 0 0</Direction>
      <Up>0 0 1</Up>
    </Detector>

    <!-- Simple Light Pipe Optical Engine -->
    <Optical type="LightPipe">
      <!-- Shape configuration -->
      <ShapeType>Circular</ShapeType>
      <TDir>out</TDir>
      <TAngle1>45 45</TAngle1>
      <TAngle2>45 45</TAngle2>
      <TDistance>1 1</TDistance>
      <TSize>50 50</TSize>

      <!-- Optional parameters -->
      <DTangle>50</DTangle>
      <DTsize>0.2</DTsize>
      <OpticalDir>1 0 0</OpticalDir>

      <!-- Input rail definition (inside LightPipe) -->
      <LineEntity name="Line" type="Line">
        <A>0 0 0</A>
        <B>60 0 -15</B>
      </LineEntity>
    </Optical>
    
  </Application>
</Configuration>
